"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar as CalendarIcon, 
  Clock, 
  MapPin, 
  Phone, 
  Mail, 
  User,
  CheckCircle,
  AlertCircle,
  Wrench,
  Info,
  Users
} from "lucide-react"
import Link from "next/link"

export default function AfspraakMakenPage() {
  const [formData, setFormData] = useState({
    serviceType: "",
    date: null as Date | null,
    timeSlot: "",
    firstName: "",
    lastName: "",
    phoneNumber: "",
    email: "",
    address: "",
    postalCode: "",
    city: "",
    description: "",
    urgency: "normaal"
  })

  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [availableSlots, setAvailableSlots] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [step, setStep] = useState(1)

  const serviceOptions = [
    { value: "loodgieter", label: "Loodgieter", duration: 2 },
    { value: "elektricien", label: "Elektricien", duration: 2 },
    { value: "slotenmaker", label: "Slotenmaker", duration: 1 },
    { value: "klusjesman", label: "Klusjesman", duration: 3 },
    { value: "meubelmontage", label: "Meubelmontage", duration: 2 },
    { value: "tegelzetter", label: "Tegelzetter", duration: 4 },
    { value: "aannemer", label: "Aannemer", duration: 8 },
    { value: "anders", label: "Anders", duration: 2 }
  ]

  const timeSlots = [
    "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", 
    "14:00", "15:00", "16:00", "17:00", "18:00", "19:00"
  ]

  const urgencyOptions = [
    { value: "spoed", label: "Spoed (vandaag)", color: "bg-red-100 text-red-800" },
    { value: "normaal", label: "Normaal (binnen 3 dagen)", color: "bg-yellow-100 text-yellow-800" },
    { value: "geen-haast", label: "Geen haast (binnen 2 weken)", color: "bg-green-100 text-green-800" }
  ]

  // Simulate available time slots based on selected date
  const handleDateSelect = (date: Date | null) => {
    setSelectedDate(date)
    if (date) {
      // Simulate some slots being unavailable
      const unavailableSlots = ["10:00", "14:00", "16:00"]
      const available = timeSlots.filter(slot => !unavailableSlots.includes(slot))
      setAvailableSlots(available)
    } else {
      setAvailableSlots([])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const nextStep = () => {
    if (step === 1 && formData.serviceType) {
      setStep(2)
    } else if (step === 2 && selectedDate && formData.timeSlot) {
      setStep(3)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl text-green-600">Afspraak Bevestigd!</CardTitle>
                  <CardDescription className="text-lg">
                    Uw afspraak is succesvol gepland. We hebben een bevestiging gestuurd naar uw e-mail.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-blue-50 rounded-lg p-6 text-left">
                    <h3 className="font-semibold text-gray-900 mb-4">Uw afspraakgegevens</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Dienst:</span>
                        <span className="font-medium">{serviceOptions.find(s => s.value === formData.serviceType)?.label}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Datum:</span>
                        <span className="font-medium">{selectedDate?.toLocaleDateString('nl-NL')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tijd:</span>
                        <span className="font-medium">{formData.timeSlot}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Locatie:</span>
                        <span className="font-medium">{formData.address}, {formData.city}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="text-sm text-yellow-800">
                          <strong>Belangrijk:</strong> Onze vakman komt 15 minuten voor de afgesproken tijd aan. 
                          Zorg ervoor dat er iemand aanwezig is om de deur te openen.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button className="flex-1" asChild>
                      <Link href="/offerte-aanvragen">
                        Offerte Aanvragen
                      </Link>
                    </Button>
                    <Button variant="outline" className="flex-1" asChild>
                      <Link href="/">
                        Terug naar Home
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Maak een Afspraak
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Plan direct een afspraak in met een van onze professionele vakmensen. 
                Kies een datum en tijd die u schikt.
              </p>
            </div>
          </div>
        </section>

        {/* Progress Steps */}
        <section className="py-8 bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <div className="flex items-center justify-between">
                {[1, 2, 3].map((stepNumber) => (
                  <div key={stepNumber} className="flex items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      step >= stepNumber 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {stepNumber}
                    </div>
                    <div className={`ml-4 text-sm font-medium ${
                      step >= stepNumber ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {stepNumber === 1 && 'Dienst kiezen'}
                      {stepNumber === 2 && 'Datum & tijd'}
                      {stepNumber === 3 && 'Gegevens'}
                    </div>
                    {stepNumber < 3 && (
                      <div className={`ml-8 w-16 h-0.5 ${
                        step > stepNumber ? 'bg-blue-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Form Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Step 1: Service Selection */}
                {step === 1 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Wrench className="w-5 h-5 text-blue-600" />
                        <span>Welke dienst heeft u nodig?</span>
                      </CardTitle>
                      <CardDescription>
                        Selecteer het type dienst waarvoor u een afspraak wilt maken
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div>
                        <Label htmlFor="serviceType">Type dienst *</Label>
                        <Select value={formData.serviceType} onValueChange={(value) => handleInputChange("serviceType", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Kies een dienst" />
                          </SelectTrigger>
                          <SelectContent>
                            {serviceOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex items-center justify-between w-full">
                                  <span>{option.label}</span>
                                  <span className="text-xs text-gray-500 ml-2">
                                    ({option.duration} uur)
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {formData.serviceType && (
                        <div className="bg-blue-50 rounded-lg p-4">
                          <div className="flex items-start space-x-3">
                            <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                            <div>
                              <h4 className="font-medium text-blue-900 mb-1">Gekozen dienst</h4>
                              <p className="text-sm text-blue-800">
                                {serviceOptions.find(s => s.value === formData.serviceType)?.label} - 
                                        Geschatte duur: {serviceOptions.find(s => s.value === formData.serviceType)?.duration} uur
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end">
                        <Button 
                          type="button" 
                          onClick={nextStep}
                          disabled={!formData.serviceType}
                        >
                          Volgende stap
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Step 2: Date and Time Selection */}
                {step === 2 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CalendarIcon className="w-5 h-5 text-purple-600" />
                        <span>Kies datum en tijd</span>
                      </CardTitle>
                      <CardDescription>
                        Selecteer een beschikbare datum en tijdslot voor uw afspraak
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid md:grid-cols-2 gap-8">
                        <div>
                          <Label className="text-base font-medium">Kies een datum</Label>
                          <Calendar
                            mode="single"
                            selected={selectedDate}
                            onSelect={handleDateSelect}
                            className="rounded-md border mt-2"
                            disabled={(date) => date < new Date() || date.getDay() === 0}
                          />
                          <p className="text-xs text-gray-500 mt-2">
                            * Zondagen zijn niet beschikbaar
                          </p>
                        </div>

                        <div>
                          <Label className="text-base font-medium">Beschikbare tijdsloten</Label>
                          <div className="mt-4">
                            {selectedDate ? (
                              <div className="grid grid-cols-3 gap-2">
                                {availableSlots.map((slot) => (
                                  <Button
                                    key={slot}
                                    type="button"
                                    variant={formData.timeSlot === slot ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => handleInputChange("timeSlot", slot)}
                                    className="w-full"
                                  >
                                    {slot}
                                  </Button>
                                ))}
                              </div>
                            ) : (
                              <div className="text-center py-8 text-gray-500">
                                <CalendarIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                                <p>Selecteer eerst een datum</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {selectedDate && formData.timeSlot && (
                        <div className="bg-green-50 rounded-lg p-4">
                          <div className="flex items-start space-x-3">
                            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                            <div>
                              <h4 className="font-medium text-green-900 mb-1">Gekozen tijdstip</h4>
                              <p className="text-sm text-green-800">
                                {selectedDate.toLocaleDateString('nl-NL', { 
                                  weekday: 'long', 
                                  year: 'numeric', 
                                  month: 'long', 
                                  day: 'numeric' 
                                })} om {formData.timeSlot}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-between">
                        <Button type="button" variant="outline" onClick={prevStep}>
                          Vorige stap
                        </Button>
                        <Button 
                          type="button" 
                          onClick={nextStep}
                          disabled={!selectedDate || !formData.timeSlot}
                        >
                          Volgende stap
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Step 3: Personal Information */}
                {step === 3 && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <User className="w-5 h-5 text-green-600" />
                          <span>Uw gegevens</span>
                        </CardTitle>
                        <CardDescription>
                          Vul uw contactgegevens in zodat we u kunnen bereiken
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="firstName">Voornaam *</Label>
                            <Input
                              id="firstName"
                              placeholder="Jan"
                              value={formData.firstName}
                              onChange={(e) => handleInputChange("firstName", e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="lastName">Achternaam *</Label>
                            <Input
                              id="lastName"
                              placeholder="Jansen"
                              value={formData.lastName}
                              onChange={(e) => handleInputChange("lastName", e.target.value)}
                            />
                          </div>
                        </div>
                        
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="phoneNumber">Telefoonnummer *</Label>
                            <Input
                              id="phoneNumber"
                              type="tel"
                              placeholder="06 12345678"
                              value={formData.phoneNumber}
                              onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="email">E-mailadres *</Label>
                            <Input
                              id="email"
                              type="email"
                              placeholder="<EMAIL>"
                              value={formData.email}
                              onChange={(e) => handleInputChange("email", e.target.value)}
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <MapPin className="w-5 h-5 text-red-600" />
                          <span>Adresgegevens</span>
                        </CardTitle>
                        <CardDescription>
                          Waar moet de afspraak plaatsvinden?
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="address">Straat en huisnummer *</Label>
                            <Input
                              id="address"
                              placeholder="Handelskade 45"
                              value={formData.address}
                              onChange={(e) => handleInputChange("address", e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="postalCode">Postcode *</Label>
                            <Input
                              id="postalCode"
                              placeholder="1011 BC"
                              value={formData.postalCode}
                              onChange={(e) => handleInputChange("postalCode", e.target.value)}
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="city">Plaats *</Label>
                          <Input
                            id="city"
                            placeholder="Amsterdam"
                            value={formData.city}
                            onChange={(e) => handleInputChange("city", e.target.value)}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Extra informatie</CardTitle>
                        <CardDescription>
                          Heeft u nog specifieke wensen of opmerkingen?
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div>
                          <Label htmlFor="description">Beschrijving (optioneel)</Label>
                          <textarea
                            id="description"
                            placeholder="Beschrijf hier eventuele details of specifieke wensen..."
                            value={formData.description}
                            onChange={(e) => handleInputChange("description", e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        
                        <div className="mt-4">
                          <Label>Urgentie</Label>
                          <Select value={formData.urgency} onValueChange={(value) => handleInputChange("urgency", value)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {urgencyOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <Badge className={option.color}>
                                    {option.label}
                                  </Badge>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Summary */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Samenvatting</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Dienst:</span>
                            <span className="font-medium">{serviceOptions.find(s => s.value === formData.serviceType)?.label}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Datum:</span>
                            <span className="font-medium">{selectedDate?.toLocaleDateString('nl-NL')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Tijd:</span>
                            <span className="font-medium">{formData.timeSlot}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Adres:</span>
                            <span className="font-medium">{formData.address}, {formData.city}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Contact:</span>
                            <span className="font-medium">{formData.firstName} {formData.lastName}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="flex justify-between">
                      <Button type="button" variant="outline" onClick={prevStep}>
                        Vorige stap
                      </Button>
                      <Button
                        type="submit"
                        size="lg"
                        disabled={isSubmitting || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.email || !formData.address || !formData.postalCode || !formData.city}
                      >
                        {isSubmitting ? "Bezig met plannen..." : "Afspraak Bevestigen"}
                      </Button>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Waarom een afspraak maken via ASklussen.nl?
              </h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Snelle Planning</h3>
                  <p className="text-gray-600 text-sm">
                    Plan direct online uw afspraak in, vaak al binnen 24 uur beschikbaar
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Professionele Vakmensen</h3>
                  <p className="text-gray-600 text-sm">
                    Al onze vakmensen zijn gecertificeerd en hebben jarenlange ervaring
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Garantie</h3>
                  <p className="text-gray-600 text-sm">
                    Wij geven garantie op al onze werkzaamheden voor uw gemoedsrust
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}