"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Star, 
  ThumbsUp, 
  MessageSquare, 
  Calendar, 
  MapPin,
  CheckCircle,
  Filter,
  Search,
  TrendingUp,
  Award,
  Users,
  Clock
} from "lucide-react"

export default function ReviewsPage() {
  const [filter, setFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("newest")
  const [showAddReview, setShowAddReview] = useState(false)
  const [newReview, setNewReview] = useState({
    name: "",
    service: "",
    rating: 5,
    comment: "",
    location: ""
  })

  const reviews = [
    {
      id: 1,
      name: "Jan de Vries",
      service: "Loodgieter",
      rating: 5,
      comment: "Uitstekende service! De loodgieter was snel ter plaatse en heeft het probleem professioneel opgelost. Zeer tevreden over de communicatie en de prijs-kwaliteitverhouding.",
      date: "2 dagen geleden",
      location: "Amsterdam",
      verified: true,
      helpful: 12
    },
    {
      id: 2,
      name: "Maria Jansen",
      service: "Elektricien",
      rating: 5,
      comment: "Super tevreden! Onze groepenkast is vervangen en alles werkt perfect. De elektricien was vriendelijk, deskundig en heeft netjes gewerkt. Absolute aanrader!",
      date: "1 week geleden",
      location: "Utrecht",
      verified: true,
      helpful: 8
    },
    {
      id: 3,
      name: "Peter Bakker",
      service: "Klusjesman",
      rating: 4,
      comment: "Goede ervaring overall. De klusjesman heeft diverse kleine klussen in huis opgelost. Alleen iets later dan afgesproken, maar het werk is wel netjes gedaan.",
      date: "2 weken geleden",
      location: "Rotterdam",
      verified: true,
      helpful: 6
    },
    {
      id: 4,
      name: "Lisa van Dijk",
      service: "Meubelmontage",
      rating: 5,
      comment: "Perfecte montage van onze IKEA keuken. De monteurs waren punctueel, werkten zeer netjes en hebben alles volgens plan opgeleverd. Top service!",
      date: "3 weken geleden",
      location: "Den Haag",
      verified: true,
      helpful: 15
    },
    {
      id: 5,
      name: "Tom Wilson",
      service: "Slotenmaker",
      rating: 5,
      comment: "In de nacht buitengesloten en de slotenmaker was binnen 20 minuten ter plaatse. Kon de deur openen zonder schade en heeft meteen een nieuw slot geplaatst. Prima service!",
      date: "1 maand geleden",
      location: "Eindhoven",
      verified: true,
      helpful: 20
    },
    {
      id: 6,
      name: "Emma de Boer",
      service: "Tegelzetter",
      rating: 4,
      comment: "Onze badkamer is prachtig getegeld. Het werk is netjes en de tegelzetter heeft goed meegedacht over de legpatronen. Alleen de voegen hadden iets strakker gemogen.",
      date: "1 maand geleden",
      location: "Groningen",
      verified: true,
      helpful: 7
    },
    {
      id: 7,
      name: "Robert Kuijpers",
      service: "Aannemer",
      rating: 5,
      comment: "Complete verbouwing van onze keuken en woonkamer. De aannemer heeft het project perfect begeleid, goede communicatie en strakke planning. Eindresultaat is fantastisch!",
      date: "2 maanden geleden",
      location: "Tilburg",
      verified: true,
      helpful: 18
    },
    {
      id: 8,
      name: "Fleur Visser",
      service: "Loodgieter",
      rating: 4,
      comment: "Snelle hulp bij een lekkage. Het probleem was snel verholpen. Alleen de prijs was iets hoger dan verwacht, maar de kwaliteit was goed.",
      date: "2 maanden geleden",
      location: "Leiden",
      verified: true,
      helpful: 5
    }
  ]

  const serviceOptions = [
    "Alle diensten",
    "Loodgieter",
    "Elektricien", 
    "Slotenmaker",
    "Klusjesman",
    "Meubelmontage",
    "Tegelzetter",
    "Aannemer"
  ]

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.comment.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.service.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = filter === "all" || review.service === filter
    
    return matchesSearch && matchesFilter
  })

  const sortedReviews = [...filteredReviews].sort((a, b) => {
    if (sortBy === "newest") {
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    } else if (sortBy === "oldest") {
      return new Date(a.date).getTime() - new Date(b.date).getTime()
    } else if (sortBy === "highest") {
      return b.rating - a.rating
    } else if (sortBy === "lowest") {
      return a.rating - b.rating
    } else if (sortBy === "helpful") {
      return b.helpful - a.helpful
    }
    return 0
  })

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ))
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would normally submit to an API
    setShowAddReview(false)
    setNewReview({
      name: "",
      service: "",
      rating: 5,
      comment: "",
      location: ""
    })
  }

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
  const totalReviews = reviews.length
  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(r => r.rating === rating).length,
    percentage: (reviews.filter(r => r.rating === rating).length / totalReviews) * 100
  }))

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Klantbeoordelingen
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Lees eerlijke reviews van onze klanten en ontdek waarom zij voor ASklussen.nl kiezen.
              </p>
            </div>
          </div>
        </section>

        {/* Stats Overview */}
        <section className="py-12 bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {averageRating.toFixed(1)}
                </div>
                <div className="flex justify-center mb-1">
                  {renderStars(Math.round(averageRating))}
                </div>
                <div className="text-gray-600">Gemiddelde beoordeling</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {totalReviews}
                </div>
                <div className="text-gray-600">Totaal reviews</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-purple-600 mb-2">98%</div>
                <div className="text-gray-600">Zou ons aanraden</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-orange-600 mb-2">4.8</div>
                <div className="text-gray-600">Service beoordeling</div>
              </div>
            </div>
          </div>
        </section>

        {/* Rating Distribution */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                Beoordelingsverdeling
              </h2>
              <div className="space-y-4">
                {ratingDistribution.map(({ rating, count, percentage }) => (
                  <div key={rating} className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 w-24">
                      <span className="text-sm font-medium">{rating}</span>
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    </div>
                    <div className="flex-1 bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-yellow-400 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <div className="text-sm text-gray-600 w-16 text-right">
                      {count}
                    </div>
                    <div className="text-sm text-gray-500 w-12 text-right">
                      {percentage.toFixed(0)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Filters and Search */}
        <section className="py-8 bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Zoek reviews..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={filter} onValueChange={setFilter}>
                  <SelectTrigger className="w-48">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Filter op dienst" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle diensten</SelectItem>
                    {serviceOptions.slice(1).map((service) => (
                      <SelectItem key={service} value={service}>
                        {service}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-48">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Sorteer op" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Nieuwste eerst</SelectItem>
                    <SelectItem value="oldest">Oudste eerst</SelectItem>
                    <SelectItem value="highest">Hoogste score</SelectItem>
                    <SelectItem value="lowest">Laagste score</SelectItem>
                    <SelectItem value="helpful">Meest behulpzaam</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button onClick={() => setShowAddReview(true)}>
                Schrijf een review
              </Button>
            </div>
          </div>
        </section>

        {/* Reviews List */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-6">
                {sortedReviews.map((review) => (
                  <Card key={review.id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <Avatar>
                            <AvatarFallback>
                              {getInitials(review.name)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold text-gray-900">{review.name}</h3>
                            <div className="flex items-center space-x-2 text-sm text-gray-600">
                              <span>{review.service}</span>
                              <span>•</span>
                              <div className="flex items-center space-x-1">
                                <MapPin className="w-3 h-3" />
                                <span>{review.location}</span>
                              </div>
                              <span>•</span>
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-3 h-3" />
                                <span>{review.date}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        {review.verified && (
                          <Badge className="bg-green-100 text-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Geverifieerd
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 mb-4">
                        <div className="flex">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-sm text-gray-600">
                          {review.rating}.0 uit 5
                        </span>
                      </div>
                      
                      <p className="text-gray-700 mb-4 leading-relaxed">
                        {review.comment}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <Button variant="ghost" size="sm" className="text-gray-600">
                          <ThumbsUp className="w-4 h-4 mr-2" />
                          Behulpzaam ({review.helpful})
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              {sortedReviews.length === 0 && (
                <div className="text-center py-12">
                  <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Geen reviews gevonden
                  </h3>
                  <p className="text-gray-600">
                    Probeer andere zoektermen of filters
                  </p>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Add Review Modal */}
        {showAddReview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
              <CardHeader>
                <CardTitle>Schrijf een review</CardTitle>
                <CardDescription>
                  Deel uw ervaring met ASklussen.nl
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmitReview} className="space-y-4">
                  <div>
                    <Label htmlFor="name">Uw naam *</Label>
                    <Input
                      id="name"
                      value={newReview.name}
                      onChange={(e) => setNewReview(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="service">Dienst *</Label>
                    <Select value={newReview.service} onValueChange={(value) => setNewReview(prev => ({ ...prev, service: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Kies een dienst" />
                      </SelectTrigger>
                      <SelectContent>
                        {serviceOptions.slice(1).map((service) => (
                          <SelectItem key={service} value={service}>
                            {service}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="location">Locatie</Label>
                    <Input
                      id="location"
                      placeholder="Uw stad/dorp"
                      value={newReview.location}
                      onChange={(e) => setNewReview(prev => ({ ...prev, location: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <Label>Beoordeling *</Label>
                    <div className="flex space-x-1 mt-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-8 h-8 cursor-pointer transition-colors ${
                            star <= newReview.rating 
                              ? "text-yellow-400 fill-current" 
                              : "text-gray-300 hover:text-yellow-400"
                          }`}
                          onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                        />
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="comment">Uw review *</Label>
                    <Textarea
                      id="comment"
                      rows={4}
                      placeholder="Deel uw ervaring..."
                      value={newReview.comment}
                      onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div className="flex space-x-3">
                    <Button type="submit" className="flex-1">
                      Plaats review
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setShowAddReview(false)}
                    >
                      Annuleren
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Trust Indicators */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Waarom onze klanten ons vertrouwen
              </h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Kwaliteitsgarantie</h3>
                  <p className="text-gray-600 text-sm">
                    Wij staan garant voor de kwaliteit van al onze diensten en vakmensen
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Echte Reviews</h3>
                  <p className="text-gray-600 text-sm">
                    Alle reviews zijn van echte klanten en worden geverifieerd door ons team
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Snelle Service</h3>
                  <p className="text-gray-600 text-sm">
                    Gemiddelde beoordeling van 4.8/5 voor onze snelheid en betrouwbaarheid
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}