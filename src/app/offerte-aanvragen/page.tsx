"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  FileText, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar, 
  Wrench, 
  Camera,
  CheckCircle,
  Calculator,
  Users,
  Clock,
  AlertCircle,
  Info
} from "lucide-react"
import Link from "next/link"

export default function OfferteAanvragenPage() {
  const searchParams = useSearchParams()
  const [formData, setFormData] = useState({
    serviceType: searchParams.get("dienst") || "",
    description: "",
    address: "",
    postalCode: "",
    city: "",
    firstName: "",
    lastName: "",
    phoneNumber: "",
    email: "",
    urgency: "normaal",
    budget: "",
    preferredStartDate: "",
    additionalInfo: ""
  })

  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [priceEstimate, setPriceEstimate] = useState<number | null>(null)

  const serviceOptions = [
    { value: "loodgieter", label: "Loodgieter", hourlyRate: 45, calloutFee: 89 },
    { value: "elektricien", label: "Elektricien", hourlyRate: 50, calloutFee: 95 },
    { value: "slotenmaker", label: "Slotenmaker", hourlyRate: 65, calloutFee: 120 },
    { value: "klusjesman", label: "Klusjesman", hourlyRate: 35, calloutFee: 65 },
    { value: "meubelmontage", label: "Meubelmontage", hourlyRate: 40, calloutFee: 75 },
    { value: "tegelzetter", label: "Tegelzetter", hourlyRate: 55, calloutFee: 450 },
    { value: "aannemer", label: "Aannemer", hourlyRate: null, calloutFee: null },
    { value: "anders", label: "Anders", hourlyRate: 40, calloutFee: 75 }
  ]

  const urgencyOptions = [
    { value: "spoed", label: "Spoed (binnen 24 uur)", multiplier: 1.5 },
    { value: "normaal", label: "Normaal (binnen 3 dagen)", multiplier: 1.0 },
    { value: "geen-haast", label: "Geen haast (binnen 2 weken)", multiplier: 0.9 }
  ]

  const complexityOptions = [
    { value: "eenvoudig", label: "Eenvoudig (1-2 uur)", hours: 2 },
    { value: "gemiddeld", label: "Gemiddeld (3-5 uur)", hours: 4 },
    { value: "complex", label: "Complex (6+ uur)", hours: 8 }
  ]

  useEffect(() => {
    // Calculate price estimate when service type changes
    if (formData.serviceType && formData.urgency) {
      const service = serviceOptions.find(s => s.value === formData.serviceType)
      const urgency = urgencyOptions.find(u => u.value === formData.urgency)
      
      if (service && service.hourlyRate && urgency) {
        // Estimate based on average complexity
        const estimatedHours = 4 // Average hours
        const basePrice = (service.hourlyRate * estimatedHours) + service.calloutFee!
        const finalPrice = basePrice * urgency.multiplier
        setPriceEstimate(Math.round(finalPrice))
      } else {
        setPriceEstimate(null)
      }
    }
  }, [formData.serviceType, formData.urgency])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setUploadedFiles(prev => [...prev, ...files])
  }

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl text-green-600">Offerte Aanvraag Verstuurd!</CardTitle>
                  <CardDescription className="text-lg">
                    Bedankt voor uw aanvraag. We nemen zo snel mogelijk contact met u op.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-blue-50 rounded-lg p-6 text-left">
                    <h3 className="font-semibold text-gray-900 mb-4">Wat gebeurt er nu?</h3>
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">1</div>
                        <div>
                          <p className="font-medium text-gray-900">Analyse van uw aanvraag</p>
                          <p className="text-sm text-gray-600">We bekijken uw aanvraag en eventuele foto's</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">2</div>
                        <div>
                          <p className="font-medium text-gray-900">Offerte opstellen</p>
                          <p className="text-sm text-gray-600">We stellen een gedetailleerde offerte op binnen 24 uur</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">3</div>
                        <div>
                          <p className="font-medium text-gray-900">Contact opnemen</p>
                          <p className="text-sm text-gray-600">We bellen of mailen u om de offerte door te nemen</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button className="flex-1" asChild>
                      <Link href="/afspraak-maken">
                        Direct Afspraak Maken
                      </Link>
                    </Button>
                    <Button variant="outline" className="flex-1" asChild>
                      <Link href="/">
                        Terug naar Home
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Offerte Aanvragen
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Vraag een vrijblijvende offerte aan en ontvang binnen 24 uur een gedetailleerde prijsopgave.
              </p>
            </div>
          </div>
        </section>

        {/* Form Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-3 gap-8">
                {/* Main Form */}
                <div className="lg:col-span-2">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Service Selection */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Wrench className="w-5 h-5 text-blue-600" />
                          <span>Welke dienst heeft u nodig?</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label htmlFor="serviceType">Type dienst *</Label>
                          <Select value={formData.serviceType} onValueChange={(value) => handleInputChange("serviceType", value)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Kies een dienst" />
                            </SelectTrigger>
                            <SelectContent>
                              {serviceOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label htmlFor="description">Beschrijf de werkzaamheden *</Label>
                          <Textarea
                            id="description"
                            placeholder="Beschrijf zo gedetailleerd mogelijk wat er gedaan moet worden..."
                            value={formData.description}
                            onChange={(e) => handleInputChange("description", e.target.value)}
                            rows={4}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Personal Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Uw gegevens</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="firstName">Voornaam *</Label>
                            <Input
                              id="firstName"
                              placeholder="Jan"
                              value={formData.firstName}
                              onChange={(e) => handleInputChange("firstName", e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="lastName">Achternaam *</Label>
                            <Input
                              id="lastName"
                              placeholder="Jansen"
                              value={formData.lastName}
                              onChange={(e) => handleInputChange("lastName", e.target.value)}
                            />
                          </div>
                        </div>
                        
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="phoneNumber">Telefoonnummer *</Label>
                            <Input
                              id="phoneNumber"
                              type="tel"
                              placeholder="06 12345678"
                              value={formData.phoneNumber}
                              onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="email">E-mailadres *</Label>
                            <Input
                              id="email"
                              type="email"
                              placeholder="<EMAIL>"
                              value={formData.email}
                              onChange={(e) => handleInputChange("email", e.target.value)}
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Location */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <MapPin className="w-5 h-5 text-red-600" />
                          <span>Locatie</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="address">Straat en huisnummer *</Label>
                            <Input
                              id="address"
                              placeholder="Handelskade 45"
                              value={formData.address}
                              onChange={(e) => handleInputChange("address", e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="postalCode">Postcode *</Label>
                            <Input
                              id="postalCode"
                              placeholder="1011 BC"
                              value={formData.postalCode}
                              onChange={(e) => handleInputChange("postalCode", e.target.value)}
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="city">Plaats *</Label>
                          <Input
                            id="city"
                            placeholder="Amsterdam"
                            value={formData.city}
                            onChange={(e) => handleInputChange("city", e.target.value)}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Timing and Preferences */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Calendar className="w-5 h-5 text-purple-600" />
                          <span>Planning en voorkeuren</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label>Urgentie</Label>
                            <Select value={formData.urgency} onValueChange={(value) => handleInputChange("urgency", value)}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {urgencyOptions.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="preferredStartDate">Gewenste startdatum</Label>
                            <Input
                              id="preferredStartDate"
                              type="date"
                              value={formData.preferredStartDate}
                              onChange={(e) => handleInputChange("preferredStartDate", e.target.value)}
                              min={new Date().toISOString().split('T')[0]}
                            />
                          </div>
                        </div>
                        
                        <div>
                          <Label htmlFor="additionalInfo">Aanvullende informatie</Label>
                          <Textarea
                            id="additionalInfo"
                            placeholder="Heeft u nog specifieke wensen of opmerkingen?"
                            value={formData.additionalInfo}
                            onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                            rows={3}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Photo Upload */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Camera className="w-5 h-5 text-green-600" />
                          <span>Foto's toevoegen (optioneel)</span>
                        </CardTitle>
                        <CardDescription>
                          Foto's helpen ons om een nauwkeurigere offerte te maken
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 mb-2">
                            Klik om foto's te uploaden
                          </p>
                          <p className="text-sm text-gray-500">
                            PNG, JPG of GIF (max. 10MB per bestand)
                          </p>
                          <Input
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleFileUpload}
                            className="hidden"
                            id="photo-upload"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById("photo-upload")?.click()}
                            className="mt-4"
                          >
                            Foto's Toevoegen
                          </Button>
                        </div>

                        {uploadedFiles.length > 0 && (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {uploadedFiles.map((file, index) => (
                              <div key={index} className="relative group">
                                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                                  <img
                                    src={URL.createObjectURL(file)}
                                    alt={`Upload ${index + 1}`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <button
                                  type="button"
                                  onClick={() => removeFile(index)}
                                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                  ×
                                </button>
                                <p className="text-xs text-gray-600 mt-1 truncate">{file.name}</p>
                              </div>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <div className="text-center">
                      <Button
                        type="submit"
                        size="lg"
                        className="px-12 py-6 text-lg"
                        disabled={isSubmitting || !formData.serviceType || !formData.description || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.email || !formData.address || !formData.postalCode || !formData.city}
                      >
                        {isSubmitting ? "Bezig met versturen..." : "Offerte Aanvragen"}
                      </Button>
                      <p className="text-sm text-gray-600 mt-4">
                        Door op 'Offerte Aanvragen' te klikken, gaat u akkoord met onze 
                        <Link href="/algemene-voorwaarden" className="text-blue-600 hover:underline">
                          {" "}algemene voorwaarden
                        </Link>
                      </p>
                    </div>
                  </form>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                  {/* Price Estimate */}
                  {priceEstimate && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Calculator className="w-5 h-5 text-green-600" />
                          <span>Prijsschatting</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center">
                          <div className="text-3xl font-bold text-green-600 mb-2">
                            €{priceEstimate}
                          </div>
                          <p className="text-sm text-gray-600">
                            Geschatte kosten exclusief btw
                          </p>
                        </div>
                        <div className="mt-4 space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Uurtarief:</span>
                            <span>€{serviceOptions.find(s => s.value === formData.serviceType)?.hourlyRate}/uur</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Voorrijkosten:</span>
                            <span>€{serviceOptions.find(s => s.value === formData.serviceType)?.calloutFee}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Urgentie toeslag:</span>
                            <span>{urgencyOptions.find(u => u.value === formData.urgency)?.multiplier === 1.5 ? "+50%" : urgencyOptions.find(u => u.value === formData.urgency)?.multiplier === 0.9 ? "-10%" : "Geen"}</span>
                          </div>
                        </div>
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                          <div className="flex items-start space-x-2">
                            <Info className="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" />
                            <p className="text-xs text-blue-800">
                              Dit is een indicatie. De definitieve prijs wordt bepaald na inspectie ter plaatse.
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Benefits */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Voordelen</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="font-medium text-gray-900">Vrijblijvend</p>
                          <p className="text-sm text-gray-600">Geen verplichtingen</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Clock className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="font-medium text-gray-900">Snel</p>
                          <p className="text-sm text-gray-600">Binnen 24 uur reactie</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Users className="w-5 h-5 text-purple-500 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="font-medium text-gray-900">Professioneel</p>
                          <p className="text-sm text-gray-600">Gecertificeerde vakmensen</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Contact Info */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Direct contact</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Phone className="w-5 h-5 text-gray-400" />
                        <div>
                          <p className="font-medium text-gray-900">085 - 123 4567</p>
                          <p className="text-sm text-gray-600">Maandag t/m zaterdag</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-gray-400" />
                        <div>
                          <p className="font-medium text-gray-900"><EMAIL></p>
                          <p className="text-sm text-gray-600">24/7 bereikbaar</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}