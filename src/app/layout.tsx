import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import LiveChat from "@/components/ui/LiveChat";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ASklussen.nl - Professionele Klussendiensten",
  description: "Vind snel en eenvoudig een betrouwbare klusser voor al uw klussen. Offertes aanvragen, afspraken inplannen en tarieven vergelijken.",
  keywords: ["klusjesman", "loodgieter", "elektricien", "slotenmaker", "handyman", "klussen", "offerte", "afspraak"],
  authors: [{ name: "ASklussen.nl" }],
  openGraph: {
    title: "ASklussen.nl - Professionele Klussendiensten",
    description: "Vind snel en eenvoudig een betrouwbare klusser voor al uw klussen. Offertes aanvragen, afspraken inplannen en tarieven vergelijken.",
    url: "https://asklussen.nl",
    siteName: "ASklussen.nl",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "ASklussen.nl - Professionele Klussendiensten",
    description: "Vind snel en eenvoudig een betrouwbare klusser voor al uw klussen.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="nl" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        {children}
        <Toaster />
        <LiveChat />
      </body>
    </html>
  );
}