"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Send, 
  CheckCircle,
  AlertCircle,
  Users,
  Building,
  Car,
  Wrench,
  MessageSquare
} from "lucide-react"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    service: "",
    message: "",
    urgency: "normaal"
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const contactInfo = [
    {
      icon: MapPin,
      title: "Bezoekadres",
      details: [
        "Handelskade 45",
        "1011 BC Amsterdam",
        "Nederland"
      ]
    },
    {
      icon: Phone,
      title: "Telefoon",
      details: [
        "085 - 123 4567",
        "Maandag t/m zaterdag: 08:00 - 18:00",
        "Zondag: Gesloten"
      ]
    },
    {
      icon: Mail,
      title: "E-mail",
      details: [
        "<EMAIL>",
        "Support: <EMAIL>",
        "Spoed: <EMAIL>"
      ]
    },
    {
      icon: Clock,
      title: "Openingstijden",
      details: [
        "Maandag - Vrijdag: 08:00 - 18:00",
        "Zaterdag: 09:00 - 17:00",
        "Zondag: Gesloten",
        "Spoeddiensten: 24/7 bereikbaar"
      ]
    }
  ]

  const services = [
    "Algemeen",
    "Loodgieter",
    "Elektricien", 
    "Slotenmaker",
    "Klusjesman",
    "Meubelmontage",
    "Tegelzetter",
    "Aannemer",
    "Facturatie",
    "Klachten",
    "Samenwerken"
  ]

  const urgencyOptions = [
    { value: "spoed", label: "Spoed (binnen 2 uur)", color: "bg-red-100 text-red-800" },
    { value: "normaal", label: "Normaal (binnen 24 uur)", color: "bg-yellow-100 text-yellow-800" },
    { value: "geen-haast", label: "Geen haast (binnen 3 dagen)", color: "bg-green-100 text-green-800" }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl text-green-600">Bericht Verstuurd!</CardTitle>
                  <CardDescription className="text-lg">
                    Bedankt voor uw bericht. We nemen zo snel mogelijk contact met u op.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-blue-50 rounded-lg p-6 text-left">
                    <h3 className="font-semibold text-gray-900 mb-4">Wat gebeurt er nu?</h3>
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">1</div>
                        <div>
                          <p className="font-medium text-gray-900">Analyse van uw bericht</p>
                          <p className="text-sm text-gray-600">We bekijken uw bericht en bepalen de juiste afdeling</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">2</div>
                        <div>
                          <p className="font-medium text-gray-900">Toewijzing aan specialist</p>
                          <p className="text-sm text-gray-600">Uw bericht wordt doorgezet naar de juiste specialist</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">3</div>
                        <div>
                          <p className="font-medium text-gray-900">Contact opnemen</p>
                          <p className="text-sm text-gray-600">We nemen contact met u op via het door u opgegeven kanaal</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button className="flex-1" asChild>
                      <a href="tel:0851234567">
                        <Phone className="w-4 h-4 mr-2" />
                        Bel Nu
                      </a>
                    </Button>
                    <Button variant="outline" className="flex-1" asChild>
                      <a href="/">
                        Terug naar Home
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Neem Contact Op
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Heeft u een vraag, klus of opmerking? Ons team staat klaar om u te helpen. 
                We zijn bereikbaar via telefoon, e-mail of het contactformulier.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Information Cards */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {contactInfo.map((info, index) => {
                const Icon = info.icon
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Icon className="w-8 h-8 text-blue-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-3">{info.title}</h3>
                      <div className="space-y-1">
                        {info.details.map((detail, idx) => (
                          <p key={idx} className="text-gray-600 text-sm">{detail}</p>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* Contact Form and Map */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MessageSquare className="w-5 h-5 text-blue-600" />
                      <span>Stuur ons een bericht</span>
                    </CardTitle>
                    <CardDescription>
                      Vul het onderstaande formulier in en we nemen zo snel mogelijk contact met u op.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="name">Naam *</Label>
                          <Input
                            id="name"
                            placeholder="Jan Jansen"
                            value={formData.name}
                            onChange={(e) => handleInputChange("name", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="email">E-mailadres *</Label>
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            required
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor="phone">Telefoonnummer</Label>
                        <Input
                          id="phone"
                          type="tel"
                          placeholder="06 12345678"
                          value={formData.phone}
                          onChange={(e) => handleInputChange("phone", e.target.value)}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="subject">Onderwerp *</Label>
                        <Input
                          id="subject"
                          placeholder="Waar gaat het over?"
                          value={formData.subject}
                          onChange={(e) => handleInputChange("subject", e.target.value)}
                          required
                        />
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="service">Dienst</Label>
                          <Select value={formData.service} onValueChange={(value) => handleInputChange("service", value)}>
                            <SelectTrigger>
                              <SelectValue placeholder="Kies een dienst" />
                            </SelectTrigger>
                            <SelectContent>
                              {services.map((service) => (
                                <SelectItem key={service} value={service}>
                                  {service}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label>Urgentie</Label>
                          <Select value={formData.urgency} onValueChange={(value) => handleInputChange("urgency", value)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {urgencyOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <Badge className={option.color}>
                                    {option.label}
                                  </Badge>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor="message">Bericht *</Label>
                        <Textarea
                          id="message"
                          placeholder="Beschrijf uw vraag of klus zo gedetailleerd mogelijk..."
                          value={formData.message}
                          onChange={(e) => handleInputChange("message", e.target.value)}
                          rows={6}
                          required
                        />
                      </div>
                      
                      <div className="flex items-start space-x-2">
                        <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                        <p className="text-sm text-gray-600">
                          Voor spoedgevallen kunt u ons direct bellen op 085 - 123 4567. 
                          Wij zijn 24/7 bereikbaar voor spoedgevallen.
                        </p>
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="w-full" 
                        size="lg"
                        disabled={isSubmitting || !formData.name || !formData.email || !formData.subject || !formData.message}
                      >
                        {isSubmitting ? "Bezig met versturen..." : (
                          <>
                            <Send className="w-4 h-4 mr-2" />
                            Verstuur Bericht
                          </>
                        )}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>

              {/* Map and Additional Info */}
              <div className="space-y-6">
                {/* Map */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MapPin className="w-5 h-5 text-red-600" />
                      <span>Onze Locatie</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                      {/* Placeholder for Google Maps */}
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
                        <div className="text-center">
                          <MapPin className="w-12 h-12 text-blue-600 mx-auto mb-2" />
                          <p className="text-blue-800 font-medium">Interactieve Kaart</p>
                          <p className="text-blue-600 text-sm">Handelskade 45, 1011 BC Amsterdam</p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 flex items-center space-x-2 text-sm text-gray-600">
                      <Car className="w-4 h-4" />
                      <span>Gratis parkeergelegenheid beschikbaar</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Snelle Acties</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="tel:0851234567">
                        <Phone className="w-4 h-4 mr-2" />
                        Direct Bellen
                      </a>
                    </Button>
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="mailto:<EMAIL>">
                        <Mail className="w-4 h-4 mr-2" />
                        E-mail Sturen
                      </a>
                    </Button>
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="/afspraak-maken">
                        <Calendar className="w-4 h-4 mr-2" />
                        Afspraak Maken
                      </a>
                    </Button>
                    <Button variant="outline" className="w-full justify-start" asChild>
                      <a href="/offerte-aanvragen">
                        <Wrench className="w-4 h-4 mr-2" />
                        Offerte Aanvragen
                      </a>
                    </Button>
                  </CardContent>
                </Card>

                {/* Business Hours */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Clock className="w-5 h-5 text-green-600" />
                      <span>Bedrijfstijden</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Maandag - Vrijdag</span>
                        <span className="font-medium">08:00 - 18:00</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Zaterdag</span>
                        <span className="font-medium">09:00 - 17:00</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Zondag</span>
                        <span className="font-medium text-gray-500">Gesloten</span>
                      </div>
                      <div className="pt-2 border-t">
                        <div className="flex justify-between">
                          <span>Spoeddiensten</span>
                          <Badge className="bg-red-100 text-red-800">24/7</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Ons Team Staat Klaar
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Onze ervaren medewerkers helpen u graag met al uw vragen en klussen.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Klantenservice</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Onze klantenservice medewerkers helpen u graag met al uw vragen.
                  </p>
                  <div className="text-sm text-gray-500">
                    <p>085 - 123 4567</p>
                    <p><EMAIL></p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Wrench className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Technische Support</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Voor technische vragen en advies over uw klus.
                  </p>
                  <div className="text-sm text-gray-500">
                    <p><EMAIL></p>
                    <p>Ma-vr: 08:00-18:00</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Building className="w-8 h-8 text-red-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Zakelijke Klanten</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Speciaal voor bedrijven en grotere projecten.
                  </p>
                  <div className="text-sm text-gray-500">
                    <p><EMAIL></p>
                    <p>Ma-vr: 08:00-17:00</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}