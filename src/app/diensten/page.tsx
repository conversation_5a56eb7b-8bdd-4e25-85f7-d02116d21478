import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Droplets, 
  Zap, 
  Lock, 
  Hammer, 
  Package, 
  Grid3X3, 
  Building,
  ArrowRight,
  Clock,
  CheckCircle,
  Euro
} from "lucide-react"
import Link from "next/link"

export default function DienstenPage() {
  const services = [
    {
      icon: Droplets,
      title: "Loodgieter",
      description: "Professionele loodgietersdiensten voor alle soorten leidingwerk, reparaties en installaties.",
      detailedDescription: "Onze gecertificeerde loodgieters staan 24/7 voor u klaar. Of het nu gaat om een lekkende kraan, verstopte afvoer, complete badkamerrenovatie of het installeren van nieuwe leidingen, wij zorgen voor een snelle en vakkundige oplossing.",
      services: [
        "Lekkages verhelpen",
        "Afvoer ontstoppen",
        "Badkamer installatie",
        "Keuken leidingwerk",
        "CV-ketel onderhoud",
        "Riolering aanleggen"
      ],
      price: "Vanaf €45 per uur",
      features: ["24/7 spoedservice", "Garantie op werk", "Vrijblijvende offerte"],
      href: "/diensten/loodgieter",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      popular: true
    },
    {
      icon: Zap,
      title: "Elektricien",
      description: "Veilige en betrouwbare elektrische installaties, reparaties en onderhoud.",
      detailedDescription: "Onze erkende elektriciens zorgen voor veilige elektrische installaties volgens de laatste normen. Van kleine reparaties tot complete elektrische installaties in nieuwbouw, wij leveren kwalitatief hoogstaand werk.",
      services: [
        "Groepenkast vervangen",
        "Stopcontacten plaatsen",
        "Verlichting installeren",
        "Aardlekschakelaar plaatsen",
        "Zonnepanelen aansluiten",
        "Elektrische auto laadpaal"
      ],
      price: "Vanaf €50 per uur",
      features: ["Erkend installateur", "NEN 3140 keuring", "Garantie op werk"],
      href: "/diensten/elektricien",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      popular: true
    },
    {
      icon: Lock,
      title: "Slotenmaker",
      description: "Snelle hulp bij slotproblemen, inbraakschade en beveiliging van uw woning.",
      detailedDescription: "Onze slotenmakers zijn gespecialiseerd in het openen van deuren zonder schade, vervangen van sloten en het verbeteren van uw beveiliging. Voor spoedgevallen zijn we 24/7 bereikbaar.",
      services: [
        "Deur openen zonder sleutel",
        "Slot vervangen",
        "Cilinder vervangen",
        "Inbraakschade herstellen",
        "Meerpuntsluiting installeren",
        "Beveiligingsadvies"
      ],
      price: "Vanaf €65 per uur",
      features: ["24/7 spoedservice", "Geen voorrijkosten", "PKVW gecertificeerd"],
      href: "/diensten/slotenmaker",
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      popular: false
    },
    {
      icon: Hammer,
      title: "Klusjesman",
      description: "Voor al uw kleine klussen in en rond het huis. Snel, betaalbaar en vakkundig.",
      detailedDescription: "Onze klusjesmannen zijn veelzijdig en kunnen u helpen met diverse kleine klussen. Of het nu gaat om het ophangen van lampen, repareren van kastjes of kleine verbouwingen, wij staan voor u klaar.",
      services: [
        "Lampen ophangen",
        "Kastjes repareren",
        "Gaten vullen en schuren",
        "Schilderwerk klein",
        "Meubels repareren",
        "Tuinonderhoud"
      ],
      price: "Vanaf €35 per uur",
      features: ["Allround klusser", "Snel beschikbaar", "Vaste prijs voor kleine klussen"],
      href: "/diensten/klusjesman",
      color: "text-green-600",
      bgColor: "bg-green-50",
      popular: false
    },
    {
      icon: Package,
      title: "Meubelmontage",
      description: "Professionele montage van meubels, keukens en IKEA producten.",
      detailedDescription: "Wij monteren uw meubels snel en vakkundig. Of het nu gaat om IKEA meubels, keukens, kasten of andere zelfbouwmeubels, wij zorgen voor een perfect resultaat volgens de instructies.",
      services: [
        "IKEA meubels monteren",
        "Keuken montage",
        "Kasten samenstellen",
        "Bedden monteren",
        "Bureaus plaatsen",
        "Tuinmeubels monteren"
      ],
      price: "Vanaf €40 per uur",
      features: ["Snel en netjes", "Inclusief afval afvoeren", "Montagegarantie"],
      href: "/diensten/meubelmontage",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      popular: false
    },
    {
      icon: Grid3X3,
      title: "Tegelzetter",
      description: "Vakmanschap in het leggen van tegels voor badkamers, keukens en vloeren.",
      detailedDescription: "Onze tegelzetters hebben jarenlange ervaring in het leggen van diverse soorten tegels. Of het nu gaat om een complete badkamer, keuken achterwand of vloer, wij leveren perfect tegelwerk.",
      services: [
        "Badkamers tegelen",
        "Keuken achterwand",
        "Vloeren leggen",
        "Terras tegelen",
        "Voegen en afwerken",
        "Oude tegels verwijderen"
      ],
      price: "Vanaf €55 per uur",
      features: ["Vakmanschap gegarandeerd", "Advies op maat", "Nette afwerking"],
      href: "/diensten/tegelzetter",
      color: "text-cyan-600",
      bgColor: "bg-cyan-50",
      popular: false
    },
    {
      icon: Building,
      title: "Aannemer",
      description: "Complete verbouwingen en renovaties onder professionele begeleiding.",
      detailedDescription: "Als aannemer begeleiden we uw complete verbouwing van begin tot eind. Van ontwerp en vergunningen tot uitvoering en oplevering, wij zorgen voor een soepel verloop van uw project.",
      services: [
        "Complete verbouwingen",
        "Aanbouw en uitbouw",
        "Renovatie projecten",
        "Dakrenovatie",
        "Funderingsherstel",
        "Projectbegeleiding"
      ],
      price: "Op aanvraag",
      features: ["Vaste projectleider", "Transparante planning", "Kwaliteitscontrole"],
      href: "/diensten/aannemer",
      color: "text-red-600",
      bgColor: "bg-red-50",
      popular: false
    }
  ]

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Onze Diensten
              </h1>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Voor elke klus de juiste vakman. Bekijk ons uitgebreide aanbod aan professionele klussendiensten 
                en vind de perfecte oplossing voor uw klus.
              </p>
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => {
                const Icon = service.icon
                return (
                  <Card key={index} className="hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
                    {service.popular && (
                      <div className="absolute top-4 right-4 z-10">
                        <Badge className="bg-orange-500 hover:bg-orange-600">
                          Populair
                        </Badge>
                      </div>
                    )}
                    
                    <CardHeader className="pb-4">
                      <div className={`w-16 h-16 ${service.bgColor} rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                        <Icon className={`w-8 h-8 ${service.color}`} />
                      </div>
                      <CardTitle className="text-2xl">{service.title}</CardTitle>
                      <CardDescription className="text-base">
                        {service.description}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-gray-900">{service.price}</span>
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <Clock className="w-4 h-4" />
                          <span>Snel beschikbaar</span>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="font-semibold text-gray-900">Veelgevraagde werkzaamheden:</h4>
                        <div className="flex flex-wrap gap-2">
                          {service.services.slice(0, 3).map((item, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {item}
                            </Badge>
                          ))}
                          {service.services.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{service.services.length - 3} meer
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-gray-600">{service.features[0]}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Euro className="w-4 h-4 text-blue-500" />
                          <span className="text-sm text-gray-600">{service.features[1]}</span>
                        </div>
                      </div>
                      
                      <div className="pt-4 flex space-x-3">
                        <Button className="flex-1" asChild>
                          <Link href={service.href}>
                            Bekijk Details
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </Link>
                        </Button>
                        <Button variant="outline" size="icon" asChild>
                          <Link href={`/offerte-aanvragen?dienst=${service.title}`}>
                            <Euro className="w-4 h-4" />
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Vind de Juiste Vakman voor Uw Klus
              </h2>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Niet zeker welke dienst u nodig heeft? Onze experts helpen u graag met het vinden 
                van de perfecte oplossing voor uw klus.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/klus-plaatsen">
                    Plaats Uw Klus
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">
                    Neem Contact Op
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}