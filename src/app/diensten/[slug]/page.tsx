import { notFound } from "next/navigation"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  CheckCircle, 
  Clock, 
  Euro, 
  Users, 
  Phone, 
  Calendar,
  Star,
  ArrowRight,
  Droplets,
  Zap,
  Lock,
  Hammer,
  Package,
  Grid3X3,
  Building
} from "lucide-react"
import Link from "next/link"

const serviceData: Record<string, any> = {
  loodgieter: {
    icon: Droplets,
    title: "Loodgieter",
    description: "Professionele loodgietersdiensten voor alle soorten leidingwerk, reparaties en installaties.",
    detailedDescription: "Onze gecertificeerde loodgieters staan 24/7 voor u klaar. Of het nu gaat om een lekkende kraan, verstop<PERSON> afvoer, complete badkamerrenovatie of het installeren van nieuwe leidingen, wij zorgen voor een snelle en vakkundige oplossing.",
    services: [
      "Lekkages verhelpen",
      "Afvoer ontstoppen",
      "Badkamer installatie",
      "Keuken leidingwerk",
      "CV-ketel onderhoud",
      "Riolering aanleggen",
      "Waterleidingen vervangen",
      "Gasleidingen aanleggen",
      "Dakgoten repareren",
      "Vloerverwarming installeren"
    ],
    price: "Vanaf €45 per uur",
    calloutPrice: "€89 - €150 (afhankelijk van de klus)",
    features: [
      { icon: CheckCircle, text: "24/7 spoedservice" },
      { icon: Users, text: "Gecertificeerde vakmensen" },
      { icon: Euro, text: "Transparante prijzen" },
      { icon: Clock, text: "Binnen 24 uur ter plaatse" }
    ],
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    process: [
      { step: 1, title: "Contact", description: "Neem contact met ons op en beschrijf uw probleem" },
      { step: 2, title: "Diagnose", description: "Onze loodgieter komt ter plaatse voor analyse" },
      { step: 3, title: "Offerte", description: "U ontvangt een vrijblijvende prijsopgave" },
      { step: 4, title: "Uitvoering", description: "Wij voeren de werkzaamheden professioneel uit" }
    ],
    faq: [
      {
        question: "Hoe snel kan een loodgieter ter plaatse zijn?",
        answer: "Voor spoedgevallen zijn we binnen 2 uur ter plaatse. Voor reguliere klussen plannen we meestal binnen 24 uur."
      },
      {
        question: "Zijn jullie ook beschikbaar in het weekend?",
        answer: "Ja, wij zijn 7 dagen per week beschikbaar, inclusief avonden en weekenden. Voor spoedgevallen zelfs 24/7."
      },
      {
        question: "Krijg ik garantie op het geleverde werk?",
        answer: "Ja, wij geven 6 maanden garantie op al onze werkzaamheden en gebruikte materialen."
      },
      {
        question: "Wat zijn de kosten voor voorrijkosten?",
        answer: "Binnen een straal van 20 km rekenen we geen voorrijkosten. Daarbuiten gelden er minimale voorrijkosten."
      }
    ]
  },
  elektricien: {
    icon: Zap,
    title: "Elektricien",
    description: "Veilige en betrouwbare elektrische installaties, reparaties en onderhoud.",
    detailedDescription: "Onze erkende elektriciens zorgen voor veilige elektrische installaties volgens de laatste normen. Van kleine reparaties tot complete elektrische installaties in nieuwbouw, wij leveren kwalitatief hoogstaand werk.",
    services: [
      "Groepenkast vervangen",
      "Stopcontacten plaatsen",
      "Verlichting installeren",
      "Aardlekschakelaar plaatsen",
      "Zonnepanelen aansluiten",
      "Elektrische auto laadpaal",
      "Domotica installeren",
      "Elektrische keuringen",
      "Kortsluiting verhelpen",
      "Bedrading aanleggen"
    ],
    price: "Vanaf €50 per uur",
    calloutPrice: "€95 - €175 (afhankelijk van de klus)",
    features: [
      { icon: CheckCircle, text: "Erkend installateur" },
      { icon: Users, text: "NEN 3140 gecertificeerd" },
      { icon: Euro, text: "Vrijblijvende offerte" },
      { icon: Clock, text: "Snelle响应" }
    ],
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    process: [
      { step: 1, title: "Advies", description: "Wij geven u vrijblijvend advies over de mogelijkheden" },
      { step: 2, title: "Plan", description: "We maken een gedetailleerd plan en offerte" },
      { step: 3, title: "Uitvoering", description: "Onze elektriciens voeren het werk veilig uit" },
      { step: 4, title: "Oplevering", description: "Wij leveren het werk op met garantiecertificaat" }
    ],
    faq: [
      {
        question: "Zijn jullie aangesloten bij een brancheorganisatie?",
        answer: "Ja, wij zijn aangesloten bij UNETO-VNI en werken volgens de laatste NEN-normen."
      },
      {
        question: "Kunnen jullie ook groepenkasten vervangen?",
        answer: "Ja, wij vervangen regelmatig groepenkasten en zorgen voor een veilige en moderne installatie."
      },
      {
        question: "Doen jullie ook zonnepanelen installaties?",
        answer: "Ja, wij kunnen complete zonnepanelen installaties verzorgen inclusief aansluiting."
      },
      {
        question: "Is er garantie op het werk?",
        answer: "Wij geven 2 jaar garantie op de installatie en 10 jaar op de gebruikte materialen."
      }
    ]
  },
  slotenmaker: {
    icon: Lock,
    title: "Slotenmaker",
    description: "Snelle hulp bij slotproblemen, inbraakschade en beveiliging van uw woning.",
    detailedDescription: "Onze slotenmakers zijn gespecialiseerd in het openen van deuren zonder schade, vervangen van sloten en het verbeteren van uw beveiliging. Voor spoedgevallen zijn we 24/7 bereikbaar.",
    services: [
      "Deur openen zonder sleutel",
      "Slot vervangen",
      "Cilinder vervangen",
      "Inbraakschade herstellen",
      "Meerpuntsluiting installeren",
      "Beveiligingsadvies",
      "Skimmingschade verhelpen",
      "Kluis openen",
      "Autodeur openen",
      "Beveiligingscamera's installeren"
    ],
    price: "Vanaf €65 per uur",
    calloutPrice: "€120 - €250 (afhankelijk van de dienst)",
    features: [
      { icon: CheckCircle, text: "24/7 spoedservice" },
      { icon: Users, text: "PKVW gecertificeerd" },
      { icon: Euro, text: "Geen voorrijkosten" },
      { icon: Clock, text: "Binnen 30 minuten ter plaatse" }
    ],
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    process: [
      { step: 1, title: "Spoedcontact", description: "Bel ons direct voor spoedgevallen" },
      { step: 2, title: "Identificatie", description: "Wij verifiëren uw identiteit voor de veiligheid" },
      { step: 3, title: "Oplossing", description: "Wij openen de deur zonder schade" },
      { step: 4, title: "Beveiliging", description: "Eventueel plaatsen we nieuw slot" }
    ],
    faq: [
      {
        question: "Hoe snel kunnen jullie ter plaatse zijn?",
        answer: "Voor spoedgevallen zijn we binnen 30 minuten ter plaatse in de regio Amsterdam."
      },
      {
        question: "Kunnen jullie elke deur openen zonder schade?",
        answer: "Ja, wij hebben speciale technieken om 95% van de deuren zonder schade te openen."
      },
      {
        question: "Wat kost het om mijn deur te openen?",
        answer: "De kosten zijn afhankelijk van het type slot en de tijd, maar gemiddeld tussen €120-€150."
      },
      {
        question: "Doen jullie ook beveiligingsadvies?",
        answer: "Ja, wij kunnen een gratis beveiligingscheck doen en adviseren over het verbeteren van uw beveiliging."
      }
    ]
  },
  klusjesman: {
    icon: Hammer,
    title: "Klusjesman",
    description: "Voor al uw kleine klussen in en rond het huis. Snel, betaalbaar en vakkundig.",
    detailedDescription: "Onze klusjesmannen zijn veelzijdig en kunnen u helpen met diverse kleine klussen. Of het nu gaat om het ophangen van lampen, repareren van kastjes of kleine verbouwingen, wij staan voor u klaar.",
    services: [
      "Lampen ophangen",
      "Kastjes repareren",
      "Gaten vullen en schuren",
      "Schilderwerk klein",
      "Meubels repareren",
      "Tuinonderhoud",
      "Plafond repareren",
      "Deuren afstellen",
      "Kozijnen repareren",
      "Smallere klussen"
    ],
    price: "Vanaf €35 per uur",
    calloutPrice: "€65 - €120 (afhankelijk van de klus)",
    features: [
      { icon: CheckCircle, text: "Allround klusser" },
      { icon: Users, text: "Ervaring in alle sectoren" },
      { icon: Euro, text: "Vaste prijs voor kleine klussen" },
      { icon: Clock, text: "Snel beschikbaar" }
    ],
    color: "text-green-600",
    bgColor: "bg-green-50",
    process: [
      { step: 1, title: "Klusbeschrijving", description: "Beschrijf uw klus zo duidelijk mogelijk" },
      { step: 2, title: "Offerte", description: "U ontvangt een duidelijke prijsopgave" },
      { step: 3, title: "Uitvoering", description: "Onze klusjesman voert de klus professioneel uit" },
      { step: 4, title: "Controle", description: "Wij controleren samen of u tevreden bent" }
    ],
    faq: [
      {
        question: "Wat is het minimale tarief voor een klus?",
        answer: "Wij rekenen een minimale afname van 1 uur. Daarna per 15 minuten doorberekend."
      },
      {
        question: "Kunnen jullie ook materiaal meenemen?",
        answer: "Ja, wij kunnen het meeste standaardmaterialen meenemen. De kosten worden apart doorberekend."
      },
      {
        question: "Doen jullie ook klussen in het weekend?",
        answer: "Ja, wij zijn ook in het weekend beschikbaar, eventueel tegen een kleine toeslag."
      },
      {
        question: "Is er garantie op het werk?",
        answer: "Wij geven 3 maanden garantie op al onze werkzaamheden."
      }
    ]
  },
  meubelmontage: {
    icon: Package,
    title: "Meubelmontage",
    description: "Professionele montage van meubels, keukens en IKEA producten.",
    detailedDescription: "Wij monteren uw meubels snel en vakkundig. Of het nu gaat om IKEA meubels, keukens, kasten of andere zelfbouwmeubels, wij zorgen voor een perfect resultaat volgens de instructies.",
    services: [
      "IKEA meubels monteren",
      "Keuken montage",
      "Kasten samenstellen",
      "Bedden monteren",
      "Bureaus plaatsen",
      "Tuinmeubels monteren",
      "Wandkasten ophangen",
      "Garderobekasten monteren",
      "Tafels monteren",
      "Stellingkasten plaatsen"
    ],
    price: "Vanaf €40 per uur",
    calloutPrice: "€75 - €150 (afhankelijk van de meubels)",
    features: [
      { icon: CheckCircle, text: "Snel en netjes" },
      { icon: Users, text: "Ervaring met alle merken" },
      { icon: Euro, text: "Inclusief afval afvoeren" },
      { icon: Clock, text: "Montagegarantie" }
    ],
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    process: [
      { step: 1, title: "Inventarisatie", description: "Wij bekijken de meubels en de ruimte" },
      { step: 2, title: "Voorbereiding", description: "Wij controleren alle onderdelen en gereedschap" },
      { step: 3, title: "Montage", description: "Wij monteren de meubels volgens instructie" },
      { step: 4, title: "Oplevering", description: "Wij ruimen netjes op en demonstreren het resultaat" }
    ],
    faq: [
      {
        question: "Hoe lang duurt het gemiddeld om meubels te monteren?",
        answer: "Dit hangt af van het type meubels. Een kast duurt gemiddeld 1-2 uur, een keuken 1-2 dagen."
      },
      {
        question: "Kunnen jullie ook meubels ophangen aan de muur?",
        answer: "Ja, wij kunnen meubels veilig aan de muur hangen inclusief het zoeken van draagbalken."
      },
      {
        question: "Doen jullie ook het afvoeren van verpakkingsmateriaal?",
        answer: "Ja, wij nemen al het verpakkingsmateriaal mee en voeren dit netjes af."
      },
      {
        question: "Is er garantie op de montage?",
        answer: "Ja, wij geven 6 maanden garantie op de montage van uw meubels."
      }
    ]
  },
  tegelzetter: {
    icon: Grid3X3,
    title: "Tegelzetter",
    description: "Vakmanschap in het leggen van tegels voor badkamers, keukens en vloeren.",
    detailedDescription: "Onze tegelzetters hebben jarenlange ervaring in het leggen van diverse soorten tegels. Of het nu gaat om een complete badkamer, keuken achterwand of vloer, wij leveren perfect tegelwerk.",
    services: [
      "Badkamers tegelen",
      "Keuken achterwand",
      "Vloeren leggen",
      "Terras tegelen",
      "Voegen en afwerken",
      "Oude tegels verwijderen",
      "Mozaïek tegels",
      "Natuursteen tegels",
      "Keramische tegels",
      "Tegelwerk repareren"
    ],
    price: "Vanaf €55 per uur",
    calloutPrice: "€450 - €85 per m² (afhankelijk van het werk)",
    features: [
      { icon: CheckCircle, text: "Vakmanschap gegarandeerd" },
      { icon: Users, text: "Jarenlange ervaring" },
      { icon: Euro, text: "Advies op maat" },
      { icon: Clock, text: "Nette afwerking" }
    ],
    color: "text-cyan-600",
    bgColor: "bg-cyan-50",
    process: [
      { step: 1, title: "Advies", description: "Wij adviseren over de beste tegels en legpatronen" },
      { step: 2, title: "Voorbereiding", description: "Wij maken de ondergrond perfect egaal" },
      { step: 3, title: "Leggen", description: "Wij leggen de tegels precies en recht" },
      { step: 4, title: "Afwerken", description: "Wij voegen en impregneren het tegelwerk" }
    ],
    faq: [
      {
        question: "Hoe lang duurt het voordat ik de badkamer weer kan gebruiken?",
        answer: "Na het voegen moet het minimaal 24 uur drogen voordat het nat wordt."
      },
      {
        question: "Kunnen jullie ook oude tegels verwijderen?",
        answer: "Ja, wij kunnen oude tegels verwijderen en de ondergrond voorbereiden voor nieuwe tegels."
      },
      {
        question: "Welke soorten tegels leggen jullie?",
        answer: "Wij leggen alle soorten tegels: keramisch, natuursteen, mozaïek, wand- en vloertegels."
      },
      {
        question: "Is er garantie op het tegelwerk?",
        answer: "Wij geven 2 jaar garantie op het legwerk en 1 jaar op de voegen."
      }
    ]
  },
  aannemer: {
    icon: Building,
    title: "Aannemer",
    description: "Complete verbouwingen en renovaties onder professionele begeleiding.",
    detailedDescription: "Als aannemer begeleiden we uw complete verbouwing van begin tot eind. Van ontwerp en vergunningen tot uitvoering en oplevering, wij zorgen voor een soepel verloop van uw project.",
    services: [
      "Complete verbouwingen",
      "Aanbouw en uitbouw",
      "Renovatie projecten",
      "Dakrenovatie",
      "Funderingsherstel",
      "Projectbegeleiding",
      "Nieuwbouw",
      "Utiliteitsbouw",
      "Woningaanpassingen",
      "Energiebesparende maatregelen"
    ],
    price: "Op aanvraag",
    calloutPrice: "Vrijblijvende offerte op maat",
    features: [
      { icon: CheckCircle, text: "Vaste projectleider" },
      { icon: Users, text: "Ervaren team" },
      { icon: Euro, text: "Transparante planning" },
      { icon: Clock, text: "Kwaliteitscontrole" }
    ],
    color: "text-red-600",
    bgColor: "bg-red-50",
    process: [
      { step: 1, title: "Ontwerp", description: "Wij maken een ontwerp en 3D visualisatie" },
      { step: 2, title: "Offerte", description: "U ontvangt een gedetailleerde projectofferte" },
      { step: 3, title: "Uitvoering", description: "Wij voeren de werkzaamheden professioneel uit" },
      { step: 4, title: "Oplevering", description: "Wij leveren het project op volgens afspraak" }
    ],
    faq: [
      {
        question: "Hoe lang duurt een gemiddelde verbouwing?",
        answer: "Dit hangt af van de omvang. Een kleine verbouwing duurt 2-4 weken, een grote verbouwing 2-6 maanden."
      },
      {
        question: "Helpen jullie ook met vergunningen?",
        answer: "Ja, wij kunnen het complete vergunningentraject voor u verzorgen."
      },
      {
        question: "Kunnen jullie ook het ontwerp verzorgen?",
        answer: "Ja, wij werken samen met architecten en interieurontwerpers voor het complete ontwerp."
      },
      {
        question: "Wat zijn de betalingsvoorwaarden?",
        answer: "Wij werken met een betalingsschema gekoppeld aan de voortgang van het project."
      }
    ]
  }
}

interface ServiceDetailPageProps {
  params: {
    slug: string
  }
}

export default function ServiceDetailPage({ params }: ServiceDetailPageProps) {
  const service = serviceData[params.slug]

  if (!service) {
    notFound()
  }

  const Icon = service.icon

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-gray-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center space-x-4 mb-6">
                <Link href="/diensten" className="text-blue-600 hover:text-blue-700 flex items-center">
                  ← Terug naar diensten
                </Link>
              </div>
              
              <div className="flex items-start space-x-6">
                <div className={`w-20 h-20 ${service.bgColor} rounded-2xl flex items-center justify-center flex-shrink-0`}>
                  <Icon className={`w-10 h-10 ${service.color}`} />
                </div>
                <div className="flex-1">
                  <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    {service.title}
                  </h1>
                  <p className="text-lg text-gray-600 mb-6">
                    {service.detailedDescription}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button size="lg" asChild>
                      <Link href={`/offerte-aanvragen?dienst=${service.title}`}>
                        Offerte Aanvragen
                      </Link>
                    </Button>
                    <Button variant="outline" size="lg" asChild>
                      <Link href="/afspraak-maken">
                        Afspraak Maken
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Service Details */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <Tabs defaultValue="services" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="services">Diensten</TabsTrigger>
                  <TabsTrigger value="process">Proces</TabsTrigger>
                  <TabsTrigger value="pricing">Tarieven</TabsTrigger>
                  <TabsTrigger value="faq">FAQ</TabsTrigger>
                </TabsList>
                
                <TabsContent value="services" className="mt-8">
                  <div className="grid lg:grid-cols-2 gap-8">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">
                        Wat wij voor u kunnen doen
                      </h2>
                      <div className="grid grid-cols-2 gap-3">
                        {service.services.map((item: string, index: number) => (
                          <div key={index} className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                            <span className="text-gray-700">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">
                        Waarom kiezen voor ons
                      </h2>
                      <div className="space-y-4">
                        {service.features.map((feature: any, index: number) => {
                          const FeatureIcon = feature.icon
                          return (
                            <div key={index} className="flex items-center space-x-3">
                              <div className={`w-12 h-12 ${service.bgColor} rounded-lg flex items-center justify-center`}>
                                <FeatureIcon className={`w-6 h-6 ${service.color}`} />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{feature.text}</h3>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="process" className="mt-8">
                  <div className="max-w-4xl mx-auto">
                    <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                      Hoe wij werken
                    </h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {service.process.map((step: any, index: number) => (
                        <Card key={index} className="text-center">
                          <CardHeader>
                            <div className={`w-16 h-16 ${service.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                              <span className="text-2xl font-bold text-gray-900">{step.step}</span>
                            </div>
                            <CardTitle className="text-lg">{step.title}</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-600">{step.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="pricing" className="mt-8">
                  <div className="max-w-4xl mx-auto">
                    <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                      Tarieven en Kosten
                    </h2>
                    <div className="grid md:grid-cols-2 gap-8">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <Euro className="w-5 h-5 text-green-600" />
                            <span>Uurtarief</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold text-gray-900 mb-2">
                            {service.price}
                          </div>
                          <p className="text-gray-600">
                            Incl. voorrijkosten binnen 20 km
                          </p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <Calendar className="w-5 h-5 text-blue-600" />
                            <span>Projectprijs</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-3xl font-bold text-gray-900 mb-2">
                            {service.calloutPrice}
                          </div>
                          <p className="text-gray-600">
                            Vaste prijs voor complete klus
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                    
                    <div className="mt-8 bg-blue-50 rounded-lg p-6">
                      <h3 className="font-semibold text-gray-900 mb-2">Wat is inbegrepen:</h3>
                      <ul className="space-y-1 text-gray-700">
                        <li>• Voorrijkosten binnen 20 km</li>
                        <li>• Geen verborgen kosten</li>
                        <li>• Garantie op werkzaamheden</li>
                        <li>• Professioneel gereedschap</li>
                        <li>• Nette afwerking</li>
                      </ul>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="faq" className="mt-8">
                  <div className="max-w-4xl mx-auto">
                    <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                      Veelgestelde Vragen
                    </h2>
                    <div className="space-y-6">
                      {service.faq.map((item: any, index: number) => (
                        <Card key={index}>
                          <CardHeader>
                            <CardTitle className="text-lg">{item.question}</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-700">{item.answer}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Klaar om te beginnen met uw {service.title.toLowerCase()} klus?
              </h2>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Neem vandaag nog contact met ons op en wij zorgen voor een snelle en professionele oplossing.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href={`/offerte-aanvragen?dienst=${service.title}`}>
                    Offerte Aanvragen
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">
                    Bel Ons: 085 - 123 4567
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}