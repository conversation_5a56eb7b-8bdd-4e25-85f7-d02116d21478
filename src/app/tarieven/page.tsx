"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { 
  Droplets, 
  Zap, 
  Lock, 
  Hammer, 
  Package, 
  Grid3X3, 
  Building,
  Search,
  Filter,
  Clock,
  Euro,
  Star,
  CheckCircle,
  Info
} from "lucide-react"
import Link from "next/link"

export default function TarievenPage() {
  const [priceFilter, setPriceFilter] = useState("all")
  const [serviceFilter, setServiceFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  const pricingData = [
    {
      id: 1,
      service: "Loodgieter",
      icon: Droplets,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      category: "loodgieter",
      hourlyRate: 45,
      calloutFee: 89,
      description: "Standaard loodgieterswerkzaamheden",
      includes: ["Voorrijkosten binnen 20 km", "Diagnose en advies", "Kleine reparaties", "Garantie op werk"],
      popular: true,
      rating: 4.8,
      responseTime: "Binnen 24 uur"
    },
    {
      id: 2,
      service: "Elektricien",
      icon: Zap,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      category: "elektricien",
      hourlyRate: 50,
      calloutFee: 95,
      description: "Elektrische installaties en reparaties",
      includes: ["NEN 3140 keuring", "Veiligheidsgarantie", "Erkend installateur", "Materialen excl."],
      popular: true,
      rating: 4.9,
      responseTime: "Binnen 24 uur"
    },
    {
      id: 3,
      service: "Slotenmaker",
      icon: Lock,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      category: "slotenmaker",
      hourlyRate: 65,
      calloutFee: 120,
      description: "24/7 spoedservice voor slotproblemen",
      includes: ["24/7 beschikbaarheid", "Geen voorrijkosten", "PKVW gecertificeerd", "Schadevrij openen"],
      popular: false,
      rating: 4.7,
      responseTime: "Binnen 30 minuten"
    },
    {
      id: 4,
      service: "Klusjesman",
      icon: Hammer,
      color: "text-green-600",
      bgColor: "bg-green-50",
      category: "klusjesman",
      hourlyRate: 35,
      calloutFee: 65,
      description: "Algemene klussen en kleine reparaties",
      includes: ["Allround dienstverlening", "Materiaal meenemen", "Nette afwerking", "Minimale afname 1 uur"],
      popular: false,
      rating: 4.6,
      responseTime: "Binnen 48 uur"
    },
    {
      id: 5,
      service: "Meubelmontage",
      icon: Package,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      category: "meubelmontage",
      hourlyRate: 40,
      calloutFee: 75,
      description: "Montage van meubels en IKEA producten",
      includes: ["Montagegarantie", "Afval afvoeren", "Inclusief gereedschap", "Nette afwerking"],
      popular: false,
      rating: 4.8,
      responseTime: "Binnen 72 uur"
    },
    {
      id: 6,
      service: "Tegelzetter",
      icon: Grid3X3,
      color: "text-cyan-600",
      bgColor: "bg-cyan-50",
      category: "tegelzetter",
      hourlyRate: 55,
      calloutFee: 450,
      description: "Professioneel tegelwerk voor vloeren en wanden",
      includes: ["Vakmanschap", "Advies op maat", "Voegen en impregneren", "Garantie op legwerk"],
      popular: false,
      rating: 4.9,
      responseTime: "Binnen 1 week"
    },
    {
      id: 7,
      service: "Aannemer",
      icon: Building,
      color: "text-red-600",
      bgColor: "bg-red-50",
      category: "aannemer",
      hourlyRate: null,
      calloutFee: null,
      description: "Complete verbouwingen en renovaties",
      includes: ["Projectbegeleiding", "Vaste prijs", "Kwaliteitscontrole", "Oplevering volgens plan"],
      popular: false,
      rating: 4.7,
      responseTime: "Op afspraak",
      customPrice: "Prijs op aanvraag"
    }
  ]

  const filteredData = pricingData.filter(item => {
    const matchesSearch = item.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesService = serviceFilter === "all" || item.category === serviceFilter
    const matchesPrice = priceFilter === "all" || 
                        (priceFilter === "low" && item.hourlyRate && item.hourlyRate < 40) ||
                        (priceFilter === "medium" && item.hourlyRate && item.hourlyRate >= 40 && item.hourlyRate < 60) ||
                        (priceFilter === "high" && item.hourlyRate && item.hourlyRate >= 60)
    
    return matchesSearch && matchesService && matchesPrice
  })

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ))
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Tarieven en Prijzen
              </h1>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Transparante prijzen voor al onze diensten. Geen verborgen kosten, 
                duidelijke afspraken en eerlijke tarieven voor professioneel werk.
              </p>
            </div>
          </div>
        </section>

        {/* Filters */}
        <section className="py-8 bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Zoek dienst..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-4">
                <Select value={serviceFilter} onValueChange={setServiceFilter}>
                  <SelectTrigger className="w-48">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Dienst type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle diensten</SelectItem>
                    <SelectItem value="loodgieter">Loodgieter</SelectItem>
                    <SelectItem value="elektricien">Elektricien</SelectItem>
                    <SelectItem value="slotenmaker">Slotenmaker</SelectItem>
                    <SelectItem value="klusjesman">Klusjesman</SelectItem>
                    <SelectItem value="meubelmontage">Meubelmontage</SelectItem>
                    <SelectItem value="tegelzetter">Tegelzetter</SelectItem>
                    <SelectItem value="aannemer">Aannemer</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={priceFilter} onValueChange={setPriceFilter}>
                  <SelectTrigger className="w-48">
                    <Euro className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Prijs range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle prijzen</SelectItem>
                    <SelectItem value="low">Laag (&lt; €40/uur)</SelectItem>
                    <SelectItem value="medium">Medium (€40-60/uur)</SelectItem>
                    <SelectItem value="high">Hoog (&gt; €60/uur)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredData.map((item) => {
                const Icon = item.icon
                return (
                  <Card key={item.id} className="hover:shadow-xl transition-all duration-300 relative overflow-hidden">
                    {item.popular && (
                      <div className="absolute top-4 right-4 z-10">
                        <Badge className="bg-orange-500 hover:bg-orange-600">
                          Populair
                        </Badge>
                      </div>
                    )}
                    
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <div className={`w-12 h-12 ${item.bgColor} rounded-xl flex items-center justify-center`}>
                          <Icon className={`w-6 h-6 ${item.color}`} />
                        </div>
                        <div className="flex items-center space-x-1">
                          {renderStars(item.rating)}
                          <span className="text-sm text-gray-600 ml-1">({item.rating})</span>
                        </div>
                      </div>
                      <CardTitle className="text-xl">{item.service}</CardTitle>
                      <CardDescription>{item.description}</CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          {item.customPrice ? (
                            <div className="text-2xl font-bold text-gray-900">{item.customPrice}</div>
                          ) : (
                            <div className="text-2xl font-bold text-gray-900">
                              €{item.hourlyRate}/uur
                            </div>
                          )}
                          {item.calloutFee && (
                            <div className="text-sm text-gray-600">
                              Voorrijkosten: €{item.calloutFee}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <Clock className="w-4 h-4" />
                          <span>{item.responseTime}</span>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="font-semibold text-gray-900">Inbegrepen:</h4>
                        <div className="space-y-1">
                          {item.includes.map((include, idx) => (
                            <div key={idx} className="flex items-center space-x-2">
                              <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{include}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="pt-4 space-y-3">
                        <Button className="w-full" asChild>
                          <Link href={`/offerte-aanvragen?dienst=${item.service}`}>
                            Offerte Aanvragen
                          </Link>
                        </Button>
                        <Button variant="outline" className="w-full" asChild>
                          <Link href={`/diensten/${item.category}`}>
                            Meer Informatie
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
            
            {filteredData.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Search className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Geen diensten gevonden
                </h3>
                <p className="text-gray-600">
                  Probeer andere zoektermen of filters
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Price Information */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Belangrijke Informatie over Prijzen
              </h2>
              
              <Tabs defaultValue="general" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="general">Algemeen</TabsTrigger>
                  <TabsTrigger value="additional">Extra Kosten</TabsTrigger>
                  <TabsTrigger value="payment">Betaling</TabsTrigger>
                  <TabsTrigger value="guarantee">Garantie</TabsTrigger>
                </TabsList>
                
                <TabsContent value="general" className="mt-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Info className="w-5 h-5 text-blue-600" />
                        <span>Algemene Prijsinformatie</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Uurtarieven</h4>
                          <p className="text-gray-600 text-sm">
                            Onze uurtarieven zijn inclusief voorrijkosten binnen 20 km van uw locatie. 
                            Daarbouw rekenen we een kleine vergoeding voor de reistijd.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Voorrijkosten</h4>
                          <p className="text-gray-600 text-sm">
                            De voorrijkosten dekken de reistijd en brandstofkosten. 
                            Voor spoedgevallen buiten reguliere uren kunnen extra kosten van toepassing zijn.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Materialen</h4>
                          <p className="text-gray-600 text-sm">
                            Materialen zijn niet inbegrepen in het uurtarief, tenzij anders vermeld. 
                            Wij gebruiken kwaliteitsmaterialen en rekenen inkoopprijs door.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Minimale Afname</h4>
                          <p className="text-gray-600 text-sm">
                            Voor kleine klussen rekenen we een minimale afname van 1 uur. 
                            Daarna wordt er per 15 minuten doorberekend.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="additional" className="mt-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Euro className="w-5 h-5 text-orange-600" />
                        <span>Mogelijke Extra Kosten</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="border-l-4 border-orange-200 pl-4">
                          <h4 className="font-semibold text-gray-900">Spoeddiensten</h4>
                          <p className="text-gray-600 text-sm">
                            Voor diensten buiten reguliere uren (avond, weekend, feestdagen) 
                            rekenen we een toeslag van 50% op het reguliere tarief.
                          </p>
                        </div>
                        <div className="border-l-4 border-orange-200 pl-4">
                          <h4 className="font-semibold text-gray-900">Parkeerkosten</h4>
                          <p className="text-gray-600 text-sm">
                            Eventuele parkeerkosten in stadscentra worden doorberekend tegen kostprijs.
                          </p>
                        </div>
                        <div className="border-l-4 border-orange-200 pl-4">
                          <h4 className="font-semibold text-gray-900">Afvoer Afval</h4>
                          <p className="text-gray-600 text-sm">
                            Voor het afvoeren van grof afval rekenen we een kleine vergoeding 
                            afhankelijk van de hoeveelheid en type afval.
                          </p>
                        </div>
                        <div className="border-l-4 border-orange-200 pl-4">
                          <h4 className="font-semibold text-gray-900">Verhuismateriaal</h4>
                          <p className="text-gray-600 text-sm">
                            Bij verhuizen of grote projecten kunnen kosten voor huur van 
                            speciaal gereedschap of materieel van toepassing zijn.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="payment" className="mt-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Euro className="w-5 h-5 text-green-600" />
                        <span>Betalingsmogelijkheden</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Betaalmethoden</h4>
                          <ul className="space-y-2 text-sm text-gray-600">
                            <li>• Contant (ter plekke)</li>
                            <li>• Pinbetaling</li>
                            <li>• Bankoverschrijving</li>
                            <li>• IDEAL (via factuur)</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Betalingstermijn</h4>
                          <ul className="space-y-2 text-sm text-gray-600">
                            <li>• Kleine klussen: direct betalen</li>
                            <li>• Grote projecten: 30 dagen</li>
                            <li>• Bedrijven: op factuur</li>
                            <li>• Reguliere klanten: op rekening</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="guarantee" className="mt-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <span>Garantie en Kwaliteit</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="bg-green-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-2">Werkgarantie</h4>
                          <p className="text-gray-600 text-sm">
                            Wij geven garantie op al onze werkzaamheden. De garantietermijn is 
                            afhankelijk van het type werk:
                          </p>
                          <ul className="mt-2 space-y-1 text-sm text-gray-600">
                            <li>• Loodgieterwerk: 6 maanden</li>
                            <li>• Elektrawerk: 2 jaar</li>
                            <li>• Overige werkzaamheden: 3-6 maanden</li>
                          </ul>
                        </div>
                        <div className="bg-blue-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-2">Materiaalgarantie</h4>
                          <p className="text-gray-600 text-sm">
                            Voor gebruikte materialen gelden de fabrieksgaranties. 
                            Wij werken uitsluitend met A-merk materialen voor maximale levensduur.
                          </p>
                        </div>
                        <div className="bg-purple-50 rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-2">Kwaliteitscontrole</h4>
                          <p className="text-gray-600 text-sm">
                            Al onze vakmensen zijn gecertificeerd en worden regelmatig 
                            bijgeschoold. Wij voeren kwaliteitscontroles uit om te zorgen 
                            dat ons werk aan de hoogste standaarden voldoet.
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-4">
                Nog Vragen over Onze Tarieven?
              </h2>
              <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
                Neem contact met ons op voor een vrijblijvende offerte op maat. 
                Wij adviseren u graag over de beste oplossing voor uw klus.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/offerte-aanvragen">
                    Vraag Offerte Aan
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                  <Link href="/contact">
                    Bel Ons: 085 - 123 4567
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}