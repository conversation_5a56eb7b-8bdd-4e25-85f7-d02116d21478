import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Heart, 
  Users, 
  Award, 
  Target, 
  Lightbulb, 
  CheckCircle,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Wrench,
  Star,
  Building,
  Clock
} from "lucide-react"
import Link from "next/link"

export default function OverOnsPage() {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Oprichter & CEO",
      bio: "Met meer dan 15 jaar ervaring in de klussenbracht, begon <PERSON>.nl met een duidelijke missie: het makkelijker maken voor mensen om betrouwbare vakmensen te vinden.",
      avatar: "/avatars/jan.jpg",
      expertise: ["Bedrijfsstrategie", "Klantenservice", "Netwerkopbouw"]
    },
    {
      name: "<PERSON>",
      role: "Operations Manager",
      bio: "<PERSON> zorgt ervoor dat alles soepel draait. Van planning tot uitvoering, zij is het brein achter onze efficiënte operaties.",
      avatar: "/avatars/maria.jpg",
      expertise: ["Operations", "Kwaliteitscontrole", "Teamcoaching"]
    },
    {
      name: "Peter Bakker",
      role: "Hoofd Klantenservice",
      bio: "Peter is het eerste aanspreekpunt voor onze klanten en zorgt voor een perfecte klantervaring van begin tot eind.",
      avatar: "/avatars/peter.jpg",
      expertise: ["Klantenservice", "Probleemoplossing", "Communicatie"]
    },
    {
      name: "Lisa van Dijk",
      role: "Marketing Manager",
      bio: "Lisa zorgt ervoor dat meer mensen ons leren kennen. Ze ontwikkelt strategieën om onze diensten onder de aandacht te brengen.",
      avatar: "/avatars/lisa.jpg",
      expertise: ["Marketing", "Content creatie", "Social Media"]
    }
  ]

  const milestones = [
    {
      year: "2014",
      title: "Het begin",
      description: "ASklussen.nl wordt opgericht met als doel het verbinden van klanten met betrouwbare vakmensen.",
      icon: Building
    },
    {
      year: "2016",
      title: "Eerste 1000 klussen",
      description: "We bereiken een belangrijke mijlpaal met meer dan 1000 succesvol voltooide klussen.",
      icon: CheckCircle
    },
    {
      year: "2018",
      title: "Landelijke dekking",
      description: "Uitbreiding naar heel Nederland met meer dan 50 vakmensen in ons netwerk.",
      icon: MapPin
    },
    {
      year: "2020",
      title: "Digitalisering",
      description: "Lancering van onze nieuwe app en online platform voor nog betere service.",
      icon: TrendingUp
    },
    {
      year: "2024",
      title: "50.000+ klussen",
      description: "We hebben meer dan 50.000 klussen succesvol voltooid met een klanttevredenheid van 98%.",
      icon: Star
    }
  ]

  const values = [
    {
      icon: Heart,
      title: "Klantgericht",
      description: "De klant staat altijd centraal. We luisteren naar uw wensen en denken met u mee voor de beste oplossing.",
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      icon: Users,
      title: "Betrouwbaar",
      description: "Al onze vakmensen zijn zorgvuldig geselecteerd en gecertificeerd. U kunt op ons vertrouwen.",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Award,
      title: "Kwaliteit",
      description: "We leveren altijd hoogwaardig werk en geven garantie op al onze diensten.",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: Target,
      title: "Transparant",
      description: "Duidelijke prijzen, geen verborgen kosten en heldere communicatie.",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: Lightbulb,
      title: "Innovatief",
      description: "We blijven innoveren en verbeteren onze diensten om u de beste ervaring te bieden.",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      icon: Clock,
      title: "Snel",
      description: "Snelle响应 en korte wachttijden. We begrijpen dat uw tijd waardevol is.",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ]

  const stats = [
    { number: "50.000+", label: "Voltooide klussen" },
    { number: "500+", label: "Vakmensen" },
    { number: "98%", label: "Klanttevredenheid" },
    { number: "10+", label: "Jaar ervaring" },
    { number: "24/7", label: "Bereikbaarheid" },
    { number: "4.8/5", label: "Gemiddelde beoordeling" }
  ]

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Over ASklussen.nl
              </h1>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                ASklussen.nl is ontstaan uit de behoefte aan een betrouwbare, transparante en 
                gebruiksvriendelijke manier om vakmensen te vinden voor allerlei klussen in en rond het huis. 
                Wat begon als een klein initiatief is uitgegroeid tot een toonaangevend platform in Nederland.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/offerte-aanvragen">
                    Offerte Aanvragen
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/diensten">
                    Bekijk Diensten
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Mission and Vision */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="mb-8">
                  <Badge className="mb-4">Onze Missie</Badge>
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">
                    Het makkelijker maken voor iedereen
                  </h2>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    Onze missie is om het voor iedereen eenvoudig te maken om betrouwbare, 
                    professionele vakmensen te vinden voor hun klussen. We geloven in transparantie, 
                    kwaliteit en uitstekende service.
                  </p>
                </div>
                
                <div>
                  <Badge className="mb-4">Onze Visie</Badge>
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">
                    Toegankelijke vakmensen voor heel Nederland
                  </h2>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    We streven ernaar het meest betrouwbare platform van Nederland te worden 
                    waar mensen snel en eenvoudig de juiste vakman kunnen vinden voor elke klus, 
                    groot of klein.
                  </p>
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-6">Waarom wij bestaan</h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-blue-200 flex-shrink-0 mt-1" />
                    <div>
                      <h4 className="font-semibold mb-1">Betrouwbaarheid</h4>
                      <p className="text-blue-100 text-sm">
                        Geen verrassingen, alleen gekwalificeerde vakmensen
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-blue-200 flex-shrink-0 mt-1" />
                    <div>
                      <h4 className="font-semibold mb-1">Transparantie</h4>
                      <p className="text-blue-100 text-sm">
                        Duidelijke prijzen en heldere communicatie
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-blue-200 flex-shrink-0 mt-1" />
                    <div>
                      <h4 className="font-semibold mb-1">Gemak</h4>
                      <p className="text-blue-100 text-sm">
                        Eenvoudig online afspraken maken en offertes vergelijken
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-blue-200 flex-shrink-0 mt-1" />
                    <div>
                      <h4 className="font-semibold mb-1">Kwaliteit</h4>
                      <p className="text-blue-100 text-sm">
                        Garantie op werk en uitstekende klantenservice
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Onze Kernwaarden
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Deze waarden vormen de basis van alles wat we doen en zijn de leidraad 
                voor onze beslissingen en acties.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {values.map((value, index) => {
                const Icon = value.icon
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-8">
                      <div className={`w-16 h-16 ${value.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                        <Icon className={`w-8 h-8 ${value.color}`} />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">
                        {value.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {value.description}
                      </p>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Onze Reis
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Van een klein idee tot een toonaangevend platform. Bekijk onze belangrijkste mijlpalen.
              </p>
            </div>
            
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-blue-200"></div>
                
                <div className="space-y-12">
                  {milestones.map((milestone, index) => {
                    const Icon = milestone.icon
                    return (
                      <div key={index} className="relative flex items-start space-x-6">
                        <div className="relative z-10">
                          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                            {milestone.year}
                          </div>
                        </div>
                        <div className="flex-1 bg-white rounded-lg p-6 shadow-sm">
                          <div className="flex items-center space-x-3 mb-3">
                            <Icon className="w-6 h-6 text-blue-600" />
                            <h3 className="text-xl font-semibold text-gray-900">
                              {milestone.title}
                            </h3>
                          </div>
                          <p className="text-gray-600">
                            {milestone.description}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Ons Team
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Ontmoet het gedreven team achter ASklussen.nl. Samen werken we elke dag 
                aan het verbeteren van onze dienstverlening.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <Avatar className="w-24 h-24 mx-auto mb-4">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback className="text-2xl">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {member.name}
                    </h3>
                    <Badge className="mb-4">{member.role}</Badge>
                    <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                      {member.bio}
                    </p>
                    <div className="space-y-1">
                      <h4 className="font-medium text-gray-900 text-sm">Expertise:</h4>
                      <div className="flex flex-wrap gap-1">
                        {member.expertise.map((skill, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                Klaar om ervaring zelf te maken?
              </h2>
              <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
                Ontdek waarom duizenden klanten voor ASklussen.nl kiezen. 
                Plaats vandaag nog uw klus of vraag een vrijblijvende offerte aan.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/klus-plaatsen">
                    <Wrench className="w-4 h-4 mr-2" />
                    Plaats Klus
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                  <Link href="/contact">
                    <Phone className="w-4 h-4 mr-2" />
                    Neem Contact Op
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}