"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Calendar, 
  Clock, 
  User, 
  Search, 
  Filter, 
  TrendingUp,
  Wrench,
  Lightbulb,
  Home,
  Car,
  Droplets,
  Zap,
  Lock,
  ChevronRight,
  ArrowRight,
  Share2,
  Heart,
  BookmarkPlus
} from "lucide-react"
import Link from "next/link"

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("newest")

  const categories = [
    { id: "all", name: "Alle artikelen", count: 24 },
    { id: "tips", name: "<PERSON><PERSON><PERSON><PERSON>", count: 8 },
    { id: "onderhoud", name: "Onderhoud", count: 6 },
    { id: "verbouwing", name: "Verbouwing", count: 5 },
    { id: "veiligheid", name: "Veiligheid", count: 3 },
    { id: "besparen", name: "Besparen", count: 2 }
  ]

  const blogPosts = [
    {
      id: 1,
      title: "10 Essentiële Klustips voor Elke Huiseigenaar",
      excerpt: "Leer de belangrijkste klussen die elke huiseigenaar moet kunnen om geld te besparen en problemen te voorkomen.",
      content: "Als huiseigenaar is het belangrijk om basis klussen zelf te kunnen uitvoeren...",
      author: "Jan de Vries",
      date: "15 november 2024",
      readTime: "8 min lezen",
      category: "tips",
      tags: ["klusjesman", "diy", "besparen"],
      image: "/blog/klus-tips.jpg",
      featured: true,
      likes: 234,
      shares: 45
    },
    {
      id: 2,
      title: "Het Onderhouden van uw CV-ketel: Een Complete Gids",
      excerpt: "Regelmatig onderhoud van uw CV-ketel verlengt de levensduur en voorkomt storingen in de winter.",
      content: "Een goed onderhouden CV-ketel is essentieel voor een warm en comfortabel huis...",
      author: "Maria Jansen",
      date: "12 november 2024",
      readTime: "12 min lezen",
      category: "onderhoud",
      tags: ["cv-ketel", "onderhoud", "verwarming"],
      image: "/blog/cv-ketel-onderhoud.jpg",
      featured: false,
      likes: 189,
      shares: 32
    },
    {
      id: 3,
      title: "Kleine Badkamer Verbouwing: Grote Impact met Klein Budget",
      excerpt: "Transformeer uw badkamer zonder fortuin te spenderen met deze slimme verbouwingstips.",
      content: "Een badkamer verbouwing hoeft niet duur te zijn om impact te hebben...",
      author: "Peter Bakker",
      date: "8 november 2024",
      readTime: "15 min lezen",
      category: "verbouwing",
      tags: ["badkamer", "verbouwing", "budget"],
      image: "/blog/badkamer-verbouwing.jpg",
      featured: true,
      likes: 312,
      shares: 67
    },
    {
      id: 4,
      title: "Energie Besparen in Huis: Praktische Tips die Direct Werken",
      excerpt: "Verlaag uw energierekening met deze eenvoudige en effectieve energiebespaartips.",
      content: "Energie besparen is niet alleen goed voor het milieu, maar ook voor uw portemonnee...",
      author: "Lisa van Dijk",
      date: "5 november 2024",
      readTime: "10 min lezen",
      category: "besparen",
      tags: ["energie", "besparen", "duurzaam"],
      image: "/blog/energie-besparen.jpg",
      featured: false,
      likes: 156,
      shares: 28
    },
    {
      id: 5,
      title: "Veiligheid Voorop: Elektrische Installaties Controleren",
      excerpt: "Regelmatige controle van uw elektrische installaties voorkomt gevaarlijke situaties.",
      content: "Elektriciteit maakt ons leven gemakkelijker, maar kan ook gevaarlijk zijn...",
      author: "Tom Wilson",
      date: "2 november 2024",
      readTime: "6 min lezen",
      category: "veiligheid",
      tags: ["elektriciteit", "veiligheid", "controle"],
      image: "/blog/elektrische-veiligheid.jpg",
      featured: false,
      likes: 98,
      shares: 15
    },
    {
      id: 6,
      title: "De Voor- en Nadelen van Zonnepanelen in 2024",
      excerpt: "Ontdek of zonnepanelen een goede investering zijn voor uw huis in de huidige markt.",
      content: "Zonnepanelen zijn populairder dan ooit, maar is het nog steeds een goede investering?",
      author: "Emma de Boer",
      date: "30 oktober 2024",
      readTime: "14 min lezen",
      category: "besparen",
      tags: ["zonnepanelen", "duurzaam", "investering"],
      image: "/blog/zonnepanelen-2024.jpg",
      featured: false,
      likes: 267,
      shares: 89
    }
  ]

  const categoryIcons = {
    tips: Lightbulb,
    onderhoud: Wrench,
    verbouwing: Home,
    veiligheid: Lock,
    besparen: TrendingUp
  }

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || post.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const sortedPosts = [...filteredPosts].sort((a, b) => {
    if (sortBy === "newest") {
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    } else if (sortBy === "oldest") {
      return new Date(a.date).getTime() - new Date(b.date).getTime()
    } else if (sortBy === "popular") {
      return b.likes - a.likes
    }
    return 0
  })

  const featuredPosts = blogPosts.filter(post => post.featured)

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <Lightbulb className="w-4 h-4" />
                <span>Blog & Tips</span>
              </div>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Handige Tips en Advies
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Ontdek praktische tips, onderhoudsgidsen en advies van onze vakmensen. 
                Leer hoe u zelf klussen kunt uitvoeren en geld kunt besparen.
              </p>
            </div>
          </div>
        </section>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="py-16 bg-white">
            <div className="container mx-auto px-4">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Uitgelichte Artikelen
                </h2>
                <p className="text-lg text-gray-600">
                  Onze populairste en meest nuttige artikelen
                </p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                {featuredPosts.slice(0, 2).map((post) => (
                  <Card key={post.id} className="overflow-hidden hover:shadow-xl transition-all-300 group">
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                            {categoryIcons[post.category as keyof typeof categoryIcons] && (
                              (() => {
                                const Icon = categoryIcons[post.category as keyof typeof categoryIcons]
                                return <Icon className="w-8 h-8 text-blue-600" />
                              })()
                            )}
                          </div>
                          <p className="text-white/80 font-medium">Blog Afbeelding</p>
                        </div>
                      </div>
                      <Badge className="absolute top-4 right-4 bg-orange-500">
                        Uitgelicht
                      </Badge>
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                        <Calendar className="w-4 h-4" />
                        <span>{post.date}</span>
                        <span>•</span>
                        <Clock className="w-4 h-4" />
                        <span>{post.readTime}</span>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback>
                              {post.author.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-700">{post.author}</span>
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/blog/${post.id}`}>
                            Lees meer
                            <ArrowRight className="w-4 h-4 ml-1" />
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Search and Filter */}
        <section className="py-8 bg-gray-50 border-b">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Zoek artikelen..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-4">
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Badge
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      className="cursor-pointer hover:bg-blue-100"
                      onClick={() => setSelectedCategory(category.id)}
                    >
                      {category.name}
                      {category.count > 0 && (
                        <span className="ml-1 text-xs">({category.count})</span>
                      )}
                    </Badge>
                  ))}
                </div>
                
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Nieuwste eerst</SelectItem>
                    <SelectItem value="oldest">Oudste eerst</SelectItem>
                    <SelectItem value="popular">Meest populair</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {sortedPosts.map((post) => {
                const Icon = categoryIcons[post.category as keyof typeof categoryIcons]
                return (
                  <Card key={post.id} className="group hover:shadow-lg transition-all-300 overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3">
                            <Icon className="w-6 h-6 text-gray-600" />
                          </div>
                          <p className="text-gray-500 text-sm">Blog Afbeelding</p>
                        </div>
                      </div>
                      <div className="absolute top-3 left-3">
                        <Badge variant="secondary" className="text-xs">
                          {post.category}
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                        <Calendar className="w-4 h-4" />
                        <span>{post.date}</span>
                        <span>•</span>
                        <Clock className="w-4 h-4" />
                        <span>{post.readTime}</span>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-3 text-sm">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="text-xs">
                              {post.author.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-700">{post.author}</span>
                        </div>
                        <div className="flex items-center space-x-3 text-gray-500">
                          <div className="flex items-center space-x-1 text-sm">
                            <Heart className="w-4 h-4" />
                            <span>{post.likes}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-sm">
                            <Share2 className="w-4 h-4" />
                            <span>{post.shares}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1 mb-4">
                        {post.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" className="flex-1" asChild>
                          <Link href={`/blog/${post.id}`}>
                            Lees artikel
                          </Link>
                        </Button>
                        <Button variant="ghost" size="icon" className="hover:bg-gray-100">
                          <BookmarkPlus className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
            
            {sortedPosts.length === 0 && (
              <div className="text-center py-12">
                <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Geen artikelen gevonden
                </h3>
                <p className="text-gray-600">
                  Probeer andere zoektermen of filters
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">
                Blijf Op de Hoogte
              </h2>
              <p className="text-lg text-blue-100 mb-8">
                Schrijf u in voor onze nieuwsbrief en ontvang wekelijks de beste klustips, 
                exclusieve aanbiedingen en het laatste nieuws.
              </p>
              
              <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Uw e-mailadres"
                  className="bg-white/10 border-white/20 text-white placeholder:text-white/70"
                />
                <Button type="submit" variant="secondary" className="flex-1">
                  Inschrijven
                </Button>
              </form>
              
              <p className="text-sm text-blue-200 mt-4">
                Wij respecteren uw privacy en sturen geen spam. U kunt zich altijd afmelden.
              </p>
            </div>
          </div>
        </section>

        {/* Popular Topics */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Populaire Onderwerpen
              </h2>
              <p className="text-lg text-gray-600">
                Ontdek onze meest gelezen artikelen per categorie
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {Object.entries(categoryIcons).map(([key, Icon]) => {
                const categoryPosts = blogPosts.filter(post => post.category === key)
                const categoryName = categories.find(c => c.id === key)?.name || key
                
                return (
                  <Card key={key} className="text-center hover:shadow-lg transition-shadow cursor-pointer">
                    <CardContent className="p-6">
                      <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Icon className="w-8 h-8 text-blue-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-2">{categoryName}</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        {categoryPosts.length} artikelen
                      </p>
                      <div className="space-y-2">
                        {categoryPosts.slice(0, 3).map((post) => (
                          <div key={post.id} className="text-left text-sm text-gray-600 hover:text-blue-600 transition-colors">
                            • {post.title}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}