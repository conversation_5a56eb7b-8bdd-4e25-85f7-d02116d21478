"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { 
  Search, 
  HelpCircle, 
  Clock, 
  Euro, 
  Users, 
  CheckCircle,
  Wrench,
  Calendar,
  Star,
  AlertCircle,
  Phone,
  Mail,
  MapPin
} from "lucide-react"
import Link from "next/link"

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeCategory, setActiveCategory] = useState("all")

  const categories = [
    { id: "all", name: "<PERSON>e vragen", icon: HelpCircle },
    { id: "general", name: "<PERSON><PERSON><PERSON><PERSON>", icon: HelpCircle },
    { id: "services", name: "<PERSON><PERSON><PERSON>", icon: Wrench },
    { id: "pricing", name: "<PERSON><PERSON><PERSON><PERSON>", icon: Euro },
    { id: "booking", name: "Afspraken", icon: Calendar },
    { id: "emergency", name: "Spoedgevallen", icon: AlertCircle }
  ]

  const faqData = [
    {
      id: 1,
      category: "general",
      question: "Wat is ASklussen.nl?",
      answer: "ASklussen.nl is een online platform dat particulieren en bedrijven helpt bij het vinden van betrouwbare vakmensen voor allerlei klussen. We verbinden klanten met gecertificeerde professionals in heel Nederland.",
      keywords: ["platform", "vakmensen", "klussen", "nederland"]
    },
    {
      id: 2,
      category: "general",
      question: "Hoe werkt ASklussen.nl?",
      answer: "Het is eenvoudig: u beschrijft uw klus, wij matchen u met geschikte vakmensen, u ontvangt offertes, en u kiest de vakman die het beste bij u past. Daarna kunt u direct een afspraak maken.",
      keywords: ["werken", "klus", "vakman", "offerte", "afspraak"]
    },
    {
      id: 3,
      category: "general",
      question: "Zijn jullie actief in heel Nederland?",
      answer: "Ja, we hebben een landelijk dekking met vakmensen in alle provincies. Of u nu in Groningen, Limburg of ergens daartussen woont, we kunnen u helpen.",
      keywords: ["nederland", "dekking", "provincies", "landelijk"]
    },
    {
      id: 4,
      category: "services",
      question: "Welke diensten bieden jullie aan?",
      answer: "We bieden een breed scala aan diensten: loodgieter, elektricien, slotenmaker, klusjesman, meubelmontage, tegelzetter en aannemer. Voor elke klus hebben we de juiste professional.",
      keywords: ["diensten", "loodgieter", "elektricien", "slotenmaker", "klusjesman"]
    },
    {
      id: 5,
      category: "services",
      question: "Hoe selecteren jullie jullie vakmensen?",
      answer: "Al onze vakmensen ondergaan een strenge selectieprocedure. We controleren certificaten, ervaring, referenties en voeren een persoonlijk gesprek. Alleen de besten worden toegelaten tot ons platform.",
      keywords: ["selectie", "vakmensen", "certificaten", "ervaring", "referenties"]
    },
    {
      id: 6,
      category: "services",
      question: "Kan ik een specifieke vakman aanvragen?",
      answer: "Ja, als u tevreden was met een bepaalde vakman, kunt u deze specifiek aanvragen voor toekomstige klussen. We proberen altijd om uw voorkeur te honoreren.",
      keywords: ["specifieke", "vakman", "voorkeur", "toekomst"]
    },
    {
      id: 7,
      category: "pricing",
      question: "Hoe worden de prijzen bepaald?",
      answer: "De prijzen worden bepaald op basis van het type klus, de benodigde tijd, materialen en de regio. Elke vakman hanteert zijn eigen tarieven, maar deze zijn altijd transparant en vooraf bekend.",
      keywords: ["prijzen", "tarieven", "kosten", "transparant", "vooraf"]
    },
    {
      id: 8,
      category: "pricing",
      question: "Zijn er verborgen kosten?",
      answer: "Nee, wij werken volledig transparant. Alle kosten worden vooraf gecommuniceerd in de offerte. Eventuele meerkosten worden altijd eerst met u overlegd.",
      keywords: ["verborgen", "kosten", "transparant", "offerte", "meerkosten"]
    },
    {
      id: 9,
      category: "pricing",
      question: "Wat zijn de betalingsmogelijkheden?",
      answer: "U kunt betalen via contant, pin, bankoverschrijving of IDEAL. Voor grotere projecten bieden we ook de mogelijkheid om in termijnen te betalen.",
      keywords: ["betaling", "contant", "pin", "overschrijving", "ideal", "termijnen"]
    },
    {
      id: 10,
      category: "booking",
      question: "Hoe snel kan ik een afspraak maken?",
      answer: "Voor reguliere klussen kunt u meestal binnen 24 uur een afspraak maken. Voor spoedgevallen zijn we 24/7 beschikbaar en kunnen we vaak binnen 2 uur ter plaatse zijn.",
      keywords: ["afspraak", "snel", "24 uur", "spoed", "2 uur"]
    },
    {
      id: 11,
      category: "booking",
      question: "Kan ik een afspraak annuleren of verplaatsen?",
      answer: "Ja, u kunt tot 24 uur voor de afspraak kosteloos annuleren of verplaatsen. Bij latere annulatie kunnen we kosten in rekening brengen voor de gereserveerde tijd.",
      keywords: ["annuleren", "verplaatsen", "24 uur", "kosten", "tijd"]
    },
    {
      id: 12,
      category: "booking",
      question: "Wat als de vakman niet komt opdagen?",
      answer: "Dit komt zelden voor, maar mocht het gebeuren, dan zorgen we direct voor een alternatieve oplossing. U krijgt hiervan bericht en we proberen de afspraak zo snel mogelijk opnieuw in te plannen.",
      keywords: ["niet komen", "opdagen", "alternatief", "oplossing", "opnieuw"]
    },
    {
      id: 13,
      category: "emergency",
      question: "Wat is een spoedgeval?",
      answer: "Spoedgevallen zijn situaties die directe aandacht vereisen, zoals ernstige lekkages, stroomuitval, een buitengesloten deur of andere noodsituaties die uw veiligheid of eigendommen bedreigen.",
      keywords: ["spoed", "nood", "direct", "veiligheid", "eigendommen"]
    },
    {
      id: 14,
      category: "emergency",
      question: "Hoe snel zijn jullie bij spoedgevallen?",
      answer: "Bij spoedgevallen zijn we 24/7 bereikbaar en proberen we binnen 2 uur ter plaatse te zijn. In drukke gebieden kunnen we soms nog sneller zijn.",
      keywords: ["spoed", "24/7", "2 uur", "snel", "ter plaatse"]
    },
    {
      id: 15,
      category: "emergency",
      question: "Kosten spoeddiensten hoger?",
      answer: "Ja, voor spoeddiensten buiten reguliere uren (avond, weekend, feestdagen) rekenen we een toeslag van 50% op het reguliere tarief. Dit wordt altijd vooraf met u gecommuniceerd.",
      keywords: ["spoed", "kosten", "toeslag", "50%", "avond", "weekend"]
    }
  ]

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = activeCategory === "all" || faq.category === activeCategory
    
    return matchesSearch && matchesCategory
  })

  const getCategoryIcon = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId)
    return category ? category.icon : HelpCircle
  }

  const getFAQsByCategory = () => {
    const grouped = filteredFAQs.reduce((acc, faq) => {
      if (!acc[faq.category]) {
        acc[faq.category] = []
      }
      acc[faq.category].push(faq)
      return acc
    }, {} as Record<string, typeof faqData>)

    return grouped
  }

  const faqsByCategory = getFAQsByCategory()

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Veelgestelde Vragen
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Vind snel antwoorden op de meest gestelde vragen over onze diensten, 
                prijzen en procedures.
              </p>
            </div>
          </div>
        </section>

        {/* Search and Categories */}
        <section className="py-8 bg-white border-b">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              {/* Search Bar */}
              <div className="relative mb-8">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Zoek in onze FAQ's..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 py-3 text-lg"
                />
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-3 justify-center">
                {categories.map((category) => {
                  const Icon = category.icon
                  const isActive = activeCategory === category.id
                  return (
                    <Button
                      key={category.id}
                      variant={isActive ? "default" : "outline"}
                      onClick={() => setActiveCategory(category.id)}
                      className="flex items-center space-x-2"
                    >
                      <Icon className="w-4 h-4" />
                      <span>{category.name}</span>
                      {category.id !== "all" && (
                        <Badge variant="secondary" className="ml-2">
                          {faqData.filter(faq => faq.category === category.id).length}
                        </Badge>
                      )}
                    </Button>
                  )
                })}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Content */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              {Object.keys(faqsByCategory).length === 0 ? (
                <div className="text-center py-12">
                  <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Geen vragen gevonden
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Probeer andere zoektermen of neem contact met ons op.
                  </p>
                  <Button asChild>
                    <Link href="/contact">
                      Neem Contact Op
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-8">
                  {Object.entries(faqsByCategory).map(([category, faqs]) => {
                    const categoryInfo = categories.find(cat => cat.id === category)
                    const CategoryIcon = categoryInfo?.icon || HelpCircle
                    const categoryName = categoryInfo?.name || category
                    
                    return (
                      <div key={category}>
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <CategoryIcon className="w-6 h-6 text-blue-600" />
                          </div>
                          <h2 className="text-2xl font-bold text-gray-900">
                            {categoryName}
                          </h2>
                          <Badge variant="outline">
                            {faqs.length} vragen
                          </Badge>
                        </div>
                        
                        <Accordion type="single" collapsible className="space-y-4">
                          {faqs.map((faq) => (
                            <AccordionItem key={faq.id} value={faq.id.toString()} className="bg-white rounded-lg">
                              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                                <span className="text-left font-medium text-gray-900">
                                  {faq.question}
                                </span>
                              </AccordionTrigger>
                              <AccordionContent className="px-6 pb-4">
                                <p className="text-gray-600 leading-relaxed">
                                  {faq.answer}
                                </p>
                              </AccordionContent>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Popular Topics */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Populaire Onderwerpen
              </h2>
              <p className="text-lg text-gray-600">
                Snelle toegang tot de meest gezochte informatie
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <Wrench className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Diensten</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Alle informatie over onze diensten en vakmensen
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/diensten">Bekijk Diensten</Link>
                  </Button>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <Euro className="w-12 h-12 text-green-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Prijzen</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Transparante prijzen en tarieven voor alle diensten
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/tarieven">Bekijk Tarieven</Link>
                  </Button>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <Calendar className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Afspraken</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Hoe u snel en eenvoudig een afspraak maakt
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/afspraak-maken">Maak Afspraak</Link>
                  </Button>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Spoed</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    24/7 bereikbaarheid voor spoedgevallen
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/contact">Spoedcontact</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Still Have Questions */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-4">
                Nog steeds vragen?
              </h2>
              <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
                Staat uw vraag er niet tussen? Ons klantenserviceteam staat klaar om u te helpen. 
                Neem contact met ons op en wij voorzien u graag van een passend antwoord.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/contact">
                    <Mail className="w-4 h-4 mr-2" />
                    Stuur een Bericht
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                  <Link href="tel:0851234567">
                    <Phone className="w-4 h-4 mr-2" />
                    Bel Ons Direct
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Contact Info */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="p-6">
                  <Phone className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Telefoon</h3>
                  <p className="text-gray-600 mb-4">
                    085 - 123 4567
                  </p>
                  <p className="text-sm text-gray-500">
                    Maandag t/m zaterdag<br />
                    08:00 - 18:00
                  </p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-6">
                  <Mail className="w-12 h-12 text-green-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">E-mail</h3>
                  <p className="text-gray-600 mb-4">
                    <EMAIL>
                  </p>
                  <p className="text-sm text-gray-500">
                    Reactie binnen 24 uur
                  </p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-6">
                  <MapPin className="w-12 h-12 text-red-600 mx-auto mb-4" />
                  <h3 className="font-semibold text-gray-900 mb-2">Adres</h3>
                  <p className="text-gray-600 mb-4">
                    Handelskade 45<br />
                    1011 BC Amsterdam
                  </p>
                  <p className="text-sm text-gray-500">
                    Bezoek op afspraak
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}