"use client"

import { useState } from "react"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { 
  Upload, 
  Calendar as CalendarIcon, 
  MapPin, 
  Clock, 
  Wrench, 
  Camera,
  X,
  Plus,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import Link from "next/link"

export default function KlusPlaatsenPage() {
  const [formData, setFormData] = useState({
    serviceType: "",
    description: "",
    address: "",
    postalCode: "",
    city: "",
    phoneNumber: "",
    email: "",
    preferredDate: null as Date | null,
    urgency: "normal",
    budget: ""
  })

  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const serviceOptions = [
    { value: "loodgieter", label: "Loodgieter" },
    { value: "elektricien", label: "Elektricien" },
    { value: "slotenmaker", label: "Slotenmaker" },
    { value: "klusjesman", label: "Klusjesman" },
    { value: "meubelmontage", label: "Meubelmontage" },
    { value: "tegelzetter", label: "Tegelzetter" },
    { value: "aannemer", label: "Aannemer" },
    { value: "anders", label: "Anders" }
  ]

  const urgencyOptions = [
    { value: "spoed", label: "Spoed (binnen 24 uur)", color: "bg-red-100 text-red-800" },
    { value: "normaal", label: "Normaal (binnen 3 dagen)", color: "bg-yellow-100 text-yellow-800" },
    { value: "geen-haast", label: "Geen haast (binnen 2 weken)", color: "bg-green-100 text-green-800" }
  ]

  const budgetOptions = [
    { value: "<100", label: "Minder dan €100" },
    { value: "100-250", label: "€100 - €250" },
    { value: "250-500", label: "€250 - €500" },
    { value: "500-1000", label: "€500 - €1.000" },
    { value: "1000-2500", label: "€1.000 - €2.500" },
    { value: ">2500", label: "Meer dan €2.500" },
    { value: "geen-idee", label: "Geen idee" }
  ]

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setUploadedFiles(prev => [...prev, ...files])
  }

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl text-green-600">Klus Succesvol Geplaatst!</CardTitle>
                  <CardDescription className="text-lg">
                    Bedankt voor het plaatsen van uw klus. We nemen zo snel mogelijk contact met u op.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-blue-50 rounded-lg p-6 text-left">
                    <h3 className="font-semibold text-gray-900 mb-4">Wat gebeurt er nu?</h3>
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">1</div>
                        <div>
                          <p className="font-medium text-gray-900">Analyse van uw klus</p>
                          <p className="text-sm text-gray-600">We bekijken uw klus en matchen deze met de juiste vakmensen</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">2</div>
                        <div>
                          <p className="font-medium text-gray-900">Offertes ontvangen</p>
                          <p className="text-sm text-gray-600">U ontvangt binnen 24 uur offertes van beschikbare vakmensen</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 text-sm">3</div>
                        <div>
                          <p className="font-medium text-gray-900">Kies uw vakman</p>
                          <p className="text-sm text-gray-600">Vergelijk offertes en kies de vakman die bij u past</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button className="flex-1" asChild>
                      <Link href="/offerte-aanvragen">
                        Direct Offerte Aanvragen
                      </Link>
                    </Button>
                    <Button variant="outline" className="flex-1" asChild>
                      <Link href="/">
                        Terug naar Home
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-white py-16">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Plaats Uw Klus
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Beschrijf uw klus en ontvang binnen 24 uur offertes van de beste vakmensen in uw regio.
              </p>
            </div>
          </div>
        </section>

        {/* Form Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Service Selection */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Wrench className="w-5 h-5 text-blue-600" />
                      <span>Wat voor klus heeft u?</span>
                    </CardTitle>
                    <CardDescription>
                      Selecteer het type dienst dat u nodig heeft
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="serviceType">Type dienst *</Label>
                      <Select value={formData.serviceType} onValueChange={(value) => handleInputChange("serviceType", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Kies een dienst" />
                        </SelectTrigger>
                        <SelectContent>
                          {serviceOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Job Description */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <AlertCircle className="w-5 h-5 text-orange-600" />
                      <span>Beschrijf uw klus</span>
                    </CardTitle>
                    <CardDescription>
                      Wees zo specifiek mogelijk, dit helpt ons om de juiste vakman te vinden
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="description">Beschrijving van de klus *</Label>
                      <Textarea
                        id="description"
                        placeholder="Beschrijf hier zo gedetailleerd mogelijk wat er gedaan moet worden..."
                        value={formData.description}
                        onChange={(e) => handleInputChange("description", e.target.value)}
                        rows={6}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Location */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MapPin className="w-5 h-5 text-red-600" />
                      <span>Waar moet de klus plaatsvinden?</span>
                    </CardTitle>
                    <CardDescription>
                      Voer het volledige adres in waar de klus uitgevoerd moet worden
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="address">Straat en huisnummer *</Label>
                        <Input
                          id="address"
                          placeholder="Voorbeeld: Handelskade 45"
                          value={formData.address}
                          onChange={(e) => handleInputChange("address", e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="postalCode">Postcode *</Label>
                        <Input
                          id="postalCode"
                          placeholder="Voorbeeld: 1011 BC"
                          value={formData.postalCode}
                          onChange={(e) => handleInputChange("postalCode", e.target.value)}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="city">Plaats *</Label>
                      <Input
                        id="city"
                        placeholder="Voorbeeld: Amsterdam"
                        value={formData.city}
                        onChange={(e) => handleInputChange("city", e.target.value)}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Contact Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Uw contactgegevens</CardTitle>
                    <CardDescription>
                      We gebruiken deze gegevens om contact met u op te nemen
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="phoneNumber">Telefoonnummer *</Label>
                        <Input
                          id="phoneNumber"
                          type="tel"
                          placeholder="06 12345678"
                          value={formData.phoneNumber}
                          onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">E-mailadres *</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Timing and Urgency */}
                <div className="grid md:grid-cols-2 gap-8">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CalendarIcon className="w-5 h-5 text-purple-600" />
                        <span>Gewenste datum</span>
                      </CardTitle>
                      <CardDescription>
                        Wanneer wilt u dat de klus wordt uitgevoerd?
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Calendar
                        mode="single"
                        selected={formData.preferredDate}
                        onSelect={(date) => handleInputChange("preferredDate", date)}
                        className="rounded-md border"
                        disabled={(date) => date < new Date()}
                      />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Clock className="w-5 h-5 text-indigo-600" />
                        <span>Urgentie en budget</span>
                      </CardTitle>
                      <CardDescription>
                        Hoe urgent is uw klus en wat is uw budget?
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Urgentie</Label>
                        <Select value={formData.urgency} onValueChange={(value) => handleInputChange("urgency", value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {urgencyOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex items-center space-x-2">
                                  <Badge className={option.color}>
                                    {option.label}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Bindicatie budget</Label>
                        <Select value={formData.budget} onValueChange={(value) => handleInputChange("budget", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Kies een budgetindicatie" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Photo Upload */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Camera className="w-5 h-5 text-green-600" />
                      <span>Voeg foto's toe (optioneel)</span>
                    </CardTitle>
                    <CardDescription>
                      Foto's helpen ons om uw klus beter te begrijpen en een nauwkeurigere offerte te geven
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="photos">Upload foto's</Label>
                      <div className="mt-2">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 mb-2">
                            Klik om foto's te uploaden of sleep ze hierheen
                          </p>
                          <p className="text-sm text-gray-500">
                            PNG, JPG of GIF (max. 10MB per bestand)
                          </p>
                          <Input
                            id="photos"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleFileUpload}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById("photos")?.click()}
                            className="mt-4"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Kies bestanden
                          </Button>
                        </div>
                      </div>
                    </div>

                    {uploadedFiles.length > 0 && (
                      <div>
                        <Label>Geüploade foto's</Label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                          {uploadedFiles.map((file, index) => (
                            <div key={index} className="relative group">
                              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                                <img
                                  src={URL.createObjectURL(file)}
                                  alt={`Upload ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <button
                                type="button"
                                onClick={() => removeFile(index)}
                                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <X className="w-3 h-3" />
                              </button>
                              <p className="text-xs text-gray-600 mt-1 truncate">{file.name}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Submit Button */}
                <div className="text-center">
                  <Button
                    type="submit"
                    size="lg"
                    className="px-12 py-6 text-lg"
                    disabled={isSubmitting || !formData.serviceType || !formData.description || !formData.address || !formData.postalCode || !formData.city || !formData.phoneNumber || !formData.email}
                  >
                    {isSubmitting ? "Bezig met plaatsen..." : "Plaats Klus"}
                  </Button>
                  <p className="text-sm text-gray-600 mt-4">
                    Door op 'Plaats Klus' te klikken, gaat u akkoord met onze 
                    <Link href="/algemene-voorwaarden" className="text-blue-600 hover:underline">
                      {" "}algemene voorwaarden
                    </Link>
                  </p>
                </div>
              </form>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Waarom uw klus plaatsen via ASklussen.nl?
              </h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Vakmensen Geverifieerd</h3>
                  <p className="text-gray-600 text-sm">
                    Alle onze vakmensen zijn gescreend en hebben de juiste certificaten
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Snel Reactie</h3>
                  <p className="text-gray-600 text-sm">
                    Binnen 24 uur ontvangt u meerdere offertes van beschikbare vakmensen
                  </p>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Euro className="w-8 h-8 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Concurrerende Prijzen</h3>
                  <p className="text-gray-600 text-sm">
                    Vergelijk offertes en kies de beste prijs-kwaliteitverhouding
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}