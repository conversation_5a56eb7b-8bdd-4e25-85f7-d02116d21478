import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, Clock, CheckCircle, Star, ArrowRight, Sparkles } from "lucide-react"

export default function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Floating service cards */}
      <div className="absolute top-20 right-10 animate-float" style={{animationDelay: '1s'}}>
        <Card className="glass shadow-xl">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Wrench className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="font-semibold text-sm">Loodgieter</p>
                <p className="text-xs text-gray-600">Vanaf €45/uur</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="absolute bottom-20 left-10 animate-float" style={{animationDelay: '3s'}}>
        <Card className="glass shadow-xl">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Wrench className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="font-semibold text-sm">Elektricien</p>
                <p className="text-xs text-gray-600">Vanaf €50/uur</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="absolute top-1/3 left-10 animate-float" style={{animationDelay: '5s'}}>
        <Card className="glass shadow-xl">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Wrench className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="font-semibold text-sm">Slotenmaker</p>
                <p className="text-xs text-gray-600">Vanaf €65/uur</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="relative z-10 container mx-auto px-4 pt-20">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8 animate-slide-in-left">
            <div className="space-y-6">
              <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                <Sparkles className="w-4 h-4" />
                <span>Professionele Klussendiensten</span>
              </div>
              
              <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
                <span className="gradient-text">Vind de juiste</span>
                <br />
                <span className="text-gray-900">vakman voor</span>
                <br />
                <span className="gradient-text">elke klus</span>
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                Snel, betrouwbaar en transparant. Vind gecertificeerde vakmensen, 
                vergelijk offertes en plan direct uw afspraak in.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="group btn-glow shadow-lg hover:shadow-xl transition-all-300 text-lg px-8 py-6" asChild>
                <Link href="/offerte-aanvragen">
                  Direct Offerte Aanvragen
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="group text-lg px-8 py-6 hover-lift" asChild>
                <Link href="/afspraak-maken">
                  Afspraak Maken
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center group hover-lift cursor-pointer">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <p className="font-semibold text-gray-900">Gecertificeerd</p>
                <p className="text-sm text-gray-600">Professioneel</p>
              </div>
              <div className="text-center group hover-lift cursor-pointer">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                  <Clock className="w-6 h-6 text-blue-600" />
                </div>
                <p className="font-semibold text-gray-900">Snel</p>
                <p className="text-sm text-gray-600">Binnen 24 uur</p>
              </div>
              <div className="text-center group hover-lift cursor-pointer">
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                  <Star className="w-6 h-6 text-yellow-600" />
                </div>
                <p className="font-semibold text-gray-900">4.8/5</p>
                <p className="text-sm text-gray-600">1000+ reviews</p>
              </div>
            </div>
          </div>

          {/* Right Content - Interactive Stats Card */}
          <div className="animate-slide-in-right">
            <div className="relative">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Real-time Beschikbaarheid</h3>
                  <p className="text-gray-600">50+ vakmensen direct beschikbaar</p>
                </div>
                
                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
                    <div className="text-3xl font-bold text-blue-600 mb-1">24/7</div>
                    <p className="text-sm text-blue-800">Bereikbaar</p>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
                    <div className="text-3xl font-bold text-green-600 mb-1">15min</div>
                    <p className="text-sm text-green-800">Response tijd</p>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
                    <div className="text-3xl font-bold text-purple-600 mb-1">98%</div>
                    <p className="text-sm text-purple-800">Tevredenheid</p>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl">
                    <div className="text-3xl font-bold text-orange-600 mb-1">10+</div>
                    <p className="text-sm text-orange-800">Jaar ervaring</p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-full">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="font-medium">Live updates beschikbaar</span>
                  </div>
                </div>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow" style={{animationDelay: '1s'}}></div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  )
}