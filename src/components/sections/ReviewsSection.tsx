import { Card, CardContent } from "@/components/ui/card"
import { Star, Quote } from "lucide-react"

export default function ReviewsSection() {
  const reviews = [
    {
      name: "<PERSON>",
      location: "Amsterdam",
      rating: 5,
      service: "Loodgieter",
      comment: "Snelle service en professioneel afgehandeld. De loodgieter was binnen 30 minuten ter plaatse en heeft het probleem direct opgelost.",
      date: "2 dagen geleden"
    },
    {
      name: "<PERSON>",
      location: "Utrecht",
      rating: 5,
      service: "Elektricien",
      comment: "Uitstekende communicatie en netjes werk. De elektricien heeft onze groepenkast vervangen en alles netjes opgeruimd.",
      date: "1 week geleden"
    },
    {
      name: "<PERSON>",
      location: "Rotterdam",
      rating: 4,
      service: "<PERSON><PERSON><PERSON><PERSON>",
      comment: "Goede prijs-kwaliteitverhouding. De klusjesman heeft diverse kleine klussen in huis opgelost. Zeer tevreden!",
      date: "2 weken geleden"
    },
    {
      name: "<PERSON>",
      location: "<PERSON>",
      rating: 5,
      service: "Meubelmontage",
      comment: "Perfecte montage van onze IKEA keuken. Alles netjes en op tijd afgeleverd. Zeker aan te raden!",
      date: "3 weken geleden"
    }
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ))
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Wat Onze Klanten Zeggen
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Duizenden tevreden klanten gingen u voor. Lees hun ervaringen met onze diensten.
          </p>
          <div className="flex items-center justify-center mt-6 space-x-2">
            <div className="flex">
              {renderStars(5)}
            </div>
            <span className="text-lg font-semibold text-gray-900">4.8/5</span>
            <span className="text-gray-600">(1,247 reviews)</span>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {reviews.map((review, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="flex">
                        {renderStars(review.rating)}
                      </div>
                    </div>
                    <h3 className="font-semibold text-gray-900">{review.name}</h3>
                    <p className="text-sm text-gray-600">{review.location}</p>
                  </div>
                  <Quote className="w-8 h-8 text-blue-200 flex-shrink-0 ml-2" />
                </div>
                
                <p className="text-gray-700 text-sm mb-4 leading-relaxed">
                  "{review.comment}"
                </p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                    {review.service}
                  </span>
                  <span>{review.date}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-4 bg-white rounded-lg shadow-sm px-6 py-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">1,247</div>
              <div className="text-sm text-gray-600">Tevreden klanten</div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">4.8/5</div>
              <div className="text-sm text-gray-600">Gemiddelde beoordeling</div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">98%</div>
              <div className="text-sm text-gray-600">Zou ons aanraden</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}