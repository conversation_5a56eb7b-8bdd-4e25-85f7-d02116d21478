import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Droplets, 
  Zap, 
  Lock, 
  Hammer, 
  Package, 
  Grid3X3, 
  Building,
  ArrowRight 
} from "lucide-react"

export default function ServicesOverview() {
  const services = [
    {
      icon: Droplets,
      title: "Loodgieter",
      description: "Leiding<PERSON>, reparaties en installaties",
      href: "/diensten/loodgieter",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Zap,
      title: "Elektricien",
      description: "Elektra, groepenkasten en verlichting",
      href: "/diensten/elektricien",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      icon: Lock,
      title: "Slotenmaker",
      description: "Sloten vervangen, deuren openen",
      href: "/diensten/slotenmaker",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    },
    {
      icon: <PERSON>,
      title: "<PERSON><PERSON><PERSON><PERSON>",
      description: "Algemene klussen en kleine reparaties",
      href: "/diensten/klusjesman",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: Package,
      title: "Meubelmontage",
      description: "Kasten, meubels en IKEA montage",
      href: "/diensten/meubelmontage",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: Grid3X3,
      title: "Tegelzetter",
      description: "Badkamers, keukens en vloeren",
      href: "/diensten/tegelzetter",
      color: "text-cyan-600",
      bgColor: "bg-cyan-50"
    },
    {
      icon: Building,
      title: "Aannemer",
      description: "Verbouwingen en renovaties",
      href: "/diensten/aannemer",
      color: "text-red-600",
      bgColor: "bg-red-50"
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Onze Diensten
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Voor elke klus de juiste vakman. Van kleine reparaties tot grote verbouwingen.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {services.map((service, index) => {
            const Icon = service.icon
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer group">
                <Link href={service.href}>
                  <CardHeader className="text-center pb-4">
                    <div className={`w-16 h-16 ${service.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                      <Icon className={`w-8 h-8 ${service.color}`} />
                    </div>
                    <CardTitle className="text-xl">{service.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-center text-blue-600 group-hover:text-blue-700 transition-colors">
                      <span className="text-sm font-medium">Meer informatie</span>
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </div>
                  </CardContent>
                </Link>
              </Card>
            )
          })}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg" asChild>
            <Link href="/diensten">
              Bekijk Alle Diensten
              <ArrowRight className="w-4 h-4 ml-2" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}