import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Wrench, Calendar, FileText, ArrowRight } from "lucide-react"

export default function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-600 to-blue-700 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Klaar om Uw Klus te Laten Oplossen?
          </h2>
          <p className="text-lg text-blue-100 max-w-2xl mx-auto">
            Neem vandaag nog contact met ons op en wij zorgen voor een snelle en professionele oplossing.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Wrench className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Klus Plaatsen</h3>
            <p className="text-blue-100 mb-4">
              Beschrijf uw klus en ontvang direct passende vakmensen
            </p>
            <Button variant="secondary" size="sm" asChild>
              <Link href="/klus-plaatsen">
                Start Nu
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </Button>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Offerte Aanvragen</h3>
            <p className="text-blue-100 mb-4">
              Vraag een vrijblijvende offerte aan binnen 2 uur
            </p>
            <Button variant="secondary" size="sm" asChild>
              <Link href="/offerte-aanvragen">
                Vraag Offerte
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </Button>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Calendar className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Afspraak Maken</h3>
            <p className="text-blue-100 mb-4">
              Plan direct een afspraak in met een beschikbare vakman
            </p>
            <Button variant="secondary" size="sm" asChild>
              <Link href="/afspraak-maken">
                Plan Afspraak
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </Button>
          </div>
        </div>

        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">Niet zeker welke dienst u nodig heeft?</h3>
            <p className="text-blue-100 mb-6">
              Bel ons direct en onze experts helpen u graag met het vinden van de juiste oplossing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="text-lg">
                Bel Nu: 085 - 123 4567
              </Button>
              <Button size="lg" variant="outline" className="text-lg border-white text-white hover:bg-white hover:text-blue-600" asChild>
                <Link href="/contact">
                  Contact Formulier
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}