import { Card, CardContent } from "@/components/ui/card"
import { 
  CheckCircle, 
  Clock, 
  Euro, 
  Users, 
  Shield, 
  Phone 
} from "lucide-react"

export default function WhyChooseUs() {
  const benefits = [
    {
      icon: CheckCircle,
      title: "Gecertificeerde Vakmensen",
      description: "Al onze klussers zijn gecertificeerd en hebben jarenlange ervaring in hun vakgebied.",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: Clock,
      title: "Snelle Service",
      description: "Binnen 24 uur een vakman ter plaatse. Voor spoedgevallen zelfs binnen 2 uur.",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: Euro,
      title: "Transparante Prijzen",
      description: "Geen verborgen kosten. Vooraf duidelijke prijsafspraken en vrijblijvende offertes.",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    },
    {
      icon: Users,
      title: "Lokaal Netwerk",
      description: "Met meer dan 50 vakmensen door het hele land altijd een professional in uw buurt.",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: Shield,
      title: "Garantie op Werk",
      description: "Wij geven garantie op al onze werkzaamheden. Mocht er iets misgaan, lossen we het op.",
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      icon: Phone,
      title: "24/7 Bereikbaar",
      description: "Voor spoedgevallen zijn we 24 uur per dag, 7 dagen per week bereikbaar.",
      color: "text-cyan-600",
      bgColor: "bg-cyan-50"
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Waarom Kiezen voor ASklussen.nl?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Wij maken het vinden van een betrouwbare klusser eenvoudig, snel en transparant.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon
            return (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-8">
                  <div className={`w-20 h-20 ${benefit.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                    <Icon className={`w-10 h-10 ${benefit.color}`} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Trust Stats */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 lg:p-12 text-white">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl lg:text-5xl font-bold mb-2">10+</div>
              <div className="text-blue-100">Jaar ervaring</div>
            </div>
            <div>
              <div className="text-4xl lg:text-5xl font-bold mb-2">50.000+</div>
              <div className="text-blue-100">Klussen voltooid</div>
            </div>
            <div>
              <div className="text-4xl lg:text-5xl font-bold mb-2">500+</div>
              <div className="text-blue-100">Vakmensen</div>
            </div>
            <div>
              <div className="text-4xl lg:text-5xl font-bold mb-2">24/7</div>
              <div className="text-blue-100">Spoedservice</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}