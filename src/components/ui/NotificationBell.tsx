"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bell, X, CheckCircle, Clock, Wrench, Calendar, Star } from "lucide-react"

interface Notification {
  id: string
  type: 'appointment' | 'quote' | 'review' | 'system'
  title: string
  message: string
  timestamp: Date
  read: boolean
  icon: React.ReactNode
  action?: {
    label: string
    onClick: () => void
  }
}

export default function NotificationBell() {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'appointment',
      title: 'Nieuwe afspraak bevestigd',
      message: 'Uw afspraak voor morgen 14:00 is bevestigd',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minuten geleden
      read: false,
      icon: <Calendar className="w-4 h-4" />,
      action: {
        label: 'Bekijk details',
        onClick: () => console.log('View appointment')
      }
    },
    {
      id: '2',
      type: 'quote',
      title: 'Nieuwe offerte ontvangen',
      message: 'U heeft een nieuwe offerte voor uw loodgietersklus',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 uur geleden
      read: false,
      icon: <Wrench className="w-4 h-4" />,
      action: {
        label: 'Bekijk offerte',
        onClick: () => console.log('View quote')
      }
    },
    {
      id: '3',
      type: 'review',
      title: 'Nieuwe review ontvangen',
      message: 'Een klant heeft een 5-sterren review achtergelaten',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 dag geleden
      read: true,
      icon: <Star className="w-4 h-4" />
    },
    {
      id: '4',
      type: 'system',
      title: 'Onderhoud gepland',
      message: 'Systeemonderhoud is gepland voor vanavond 23:00',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 dagen geleden
      read: true,
      icon: <Clock className="w-4 h-4" />
    }
  ])

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Zojuist'
    if (diffInMinutes < 60) return `${diffInMinutes} min geleden`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}u geleden`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d geleden`
    
    return date.toLocaleDateString('nl-NL')
  }

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'appointment': return 'bg-blue-50 border-blue-200'
      case 'quote': return 'bg-green-50 border-green-200'
      case 'review': return 'bg-yellow-50 border-yellow-200'
      case 'system': return 'bg-gray-50 border-gray-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="relative hover:bg-gray-100"
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <Badge 
            className="absolute -top-1 -right-1 w-5 h-5 flex items-center justify-center p-0 text-xs bg-red-500 hover:bg-red-600"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Notification panel */}
          <Card className="absolute right-0 top-12 w-96 max-h-96 z-50 shadow-xl border">
            <CardContent className="p-0">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="font-semibold text-lg">Meldingen</h3>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={markAllAsRead}
                      className="text-xs"
                    >
                      Alles gelezen
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsOpen(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              {/* Notifications list */}
              <div className="max-h-80 overflow-y-auto custom-scrollbar">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <Bell className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Geen meldingen</p>
                  </div>
                ) : (
                  notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-b hover:bg-gray-50 transition-colors ${
                        !notification.read ? 'bg-blue-50/50' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${getNotificationColor(notification.type)}`}>
                          {notification.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className={`font-medium text-sm ${
                              !notification.read ? 'text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                            </h4>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-500">
                                {formatTime(notification.timestamp)}
                              </span>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                            {notification.message}
                          </p>
                          {notification.action && (
                            <div className="flex items-center justify-between">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={notification.action.onClick}
                                className="text-xs"
                              >
                                {notification.action.label}
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => removeNotification(notification.id)}
                                className="h-6 w-6"
                              >
                                <X className="w-3 h-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
              
              {/* Footer */}
              <div className="p-3 border-t bg-gray-50">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-gray-600"
                  onClick={() => console.log('View all notifications')}
                >
                  Alle meldingen bekijken
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}