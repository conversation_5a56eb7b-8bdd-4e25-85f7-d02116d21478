"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  MessageSquare, 
  X, 
  Minimize2, 
  Maximize2, 
  Send, 
  Phone,
  Mail,
  Clock,
  CheckCircle,
  Bot,
  User
} from "lucide-react"

interface Message {
  id: string
  type: 'user' | 'bot' | 'agent'
  content: string
  timestamp: Date
  sender?: string
}

export default function LiveChat() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: 'Hallo! Welkom bij ASklussen.nl. Hoe kan ik u helpen?',
      timestamp: new Date(),
      sender: 'Chatbot'
    }
  ])
  const [newMessage, setNewMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)

  const quickResponses = [
    'Ik wil een offerte aanvragen',
    'Wanneer kan er een vakman komen?',
    'Wat zijn jullie tarieven?',
    'Ik heb een spoedgeval'
  ]

  useEffect(() => {
    if (isOpen && !isMinimized) {
      setUnreadCount(0)
    }
  }, [isOpen, isMinimized])

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setNewMessage('')
    setIsTyping(true)

    // Simulate bot response
    setTimeout(() => {
      const botResponse = getBotResponse(content)
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: botResponse,
        timestamp: new Date(),
        sender: 'Chatbot'
      }
      setMessages(prev => [...prev, botMessage])
      setIsTyping(false)
    }, 1000 + Math.random() * 2000)
  }

  const getBotResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase()
    
    if (message.includes('offerte') || message.includes('prijs')) {
      return 'Ik kan u helpen met het aanvragen van een offerte. Klik op de "Offerte Aanvragen" knop in het menu of vertel me wat voor klus u heeft, dan kan ik u direct doorverbinden met de juiste afdeling.'
    }
    
    if (message.includes('spoed') || message.includes('urgent')) {
      return 'Voor spoedgevallen kunt u ons direct bellen op 085 - 123 4567. Wij zijn 24/7 bereikbaar voor spoedgevallen. Ik kan u ook direct doorverbinden met een medewerker.'
    }
    
    if (message.includes('tarief') || message.includes('kosten')) {
      return 'Onze tarieven variëren per dienst. Een loodgieter kost vanaf €45 per uur, een elektricien vanaf €50 per uur. Voor een gedetailleerd overzicht kunt u onze tarievenpagina bekijken. Wilt u dat ik u daarheen help?'
    }
    
    if (message.includes('vakman') || message.includes('afspraak')) {
      return 'U kunt eenvoudig een afspraak maken via onze website. Klik op "Afspraak Maken" en volg de stappen. Meestal kunnen we binnen 24 uur een vakman bij u plannen. Wilt u dat ik u help met het plannen?'
    }
    
    return 'Ik begrijp uw vraag. Ik kan u het beste verder helpen als u contact opneemt met onze klantenservice. Zij zijn bereikbaar op 085 - 123 4567 <NAME_EMAIL>. Kan ik u nog ergens anders mee helpen?'
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('nl-NL', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const handleQuickResponse = (response: string) => {
    handleSendMessage(response)
  }

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          size="lg"
          className="rounded-full w-16 h-16 shadow-lg hover:shadow-xl transition-all-300 group relative"
        >
          <MessageSquare className="w-6 h-6" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 w-6 h-6 flex items-center justify-center p-0 text-xs bg-red-500">
              {unreadCount}
            </Badge>
          )}
          <span className="absolute -top-20 right-0 bg-gray-900 text-white px-3 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Chat met ons
          </span>
        </Button>
      </div>
    )
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 transition-all-300 ${
      isMinimized ? 'w-80' : 'w-96'
    }`}>
      <Card className="shadow-2xl border-0">
        {!isMinimized && (
          <>
            <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <Avatar className="w-10 h-10">
                      <AvatarFallback className="bg-blue-500">
                        <Bot className="w-5 h-5" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
                  </div>
                  <div>
                    <CardTitle className="text-lg">Live Chat</CardTitle>
                    <p className="text-blue-100 text-sm">We reageren snel</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsMinimized(true)}
                    className="text-white hover:bg-white/20"
                  >
                    <Minimize2 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsOpen(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="p-0">
              {/* Messages */}
              <div className="h-96 overflow-y-auto custom-scrollbar p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                        message.type === 'user'
                          ? 'bg-blue-600 text-white'
                          : message.type === 'agent'
                          ? 'bg-gray-100 text-gray-900'
                          : 'bg-blue-50 text-gray-900'
                      }`}
                    >
                      {message.type !== 'user' && (
                        <div className="flex items-center space-x-2 mb-1">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className={`text-xs ${
                              message.type === 'agent' ? 'bg-gray-600' : 'bg-blue-600'
                            }`}>
                              {message.type === 'agent' ? (
                                <User className="w-3 h-3" />
                              ) : (
                                <Bot className="w-3 h-3" />
                              )}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-xs font-medium">
                            {message.sender}
                          </span>
                        </div>
                      )}
                      <p className="text-sm">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.type === 'user' ? 'text-blue-200' : 'text-gray-500'
                      }`}>
                        {formatTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-2xl px-4 py-3">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                        <span className="text-sm text-gray-600">Typt...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Quick Responses */}
              {messages.length === 1 && (
                <div className="p-4 border-t">
                  <p className="text-sm text-gray-600 mb-3">Veelgestelde vragen:</p>
                  <div className="flex flex-wrap gap-2">
                    {quickResponses.map((response, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickResponse(response)}
                        className="text-xs"
                      >
                        {response}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Input */}
              <div className="p-4 border-t">
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    handleSendMessage(newMessage)
                  }}
                  className="flex space-x-2"
                >
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Typ uw bericht..."
                    className="flex-1"
                  />
                  <Button type="submit" size="icon" disabled={!newMessage.trim() || isTyping}>
                    <Send className="w-4 h-4" />
                  </Button>
                </form>
              </div>
            </CardContent>
          </>
        )}
        
        {/* Minimized state */}
        {isMinimized && (
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-blue-500">
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute bottom-0 right-0 w-2 h-2 bg-green-400 rounded-full border border-white"></div>
                </div>
                <div>
                  <p className="font-medium text-sm">Live Chat</p>
                  <p className="text-xs text-gray-600">Online</p>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsMinimized(false)}
                  className="h-8 w-8"
                >
                  <Maximize2 className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                  className="h-8 w-8"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}