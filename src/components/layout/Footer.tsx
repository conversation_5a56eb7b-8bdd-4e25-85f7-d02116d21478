import Link from "next/link"
import { Phone, Mail, MapPin, Facebook, Twitter, Instagram, Linkedin } from "lucide-react"

export default function Footer() {
  const services = [
    { name: "Loodgieter", href: "/diensten/loodgieter" },
    { name: "Elektricien", href: "/diensten/elektricien" },
    { name: "<PERSON>lotenmaker", href: "/diensten/slotenmaker" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/diensten/klusjesman" },
    { name: "Meubelmontage", href: "/diensten/meubelmontage" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/diensten/tegelzetter" },
    { name: "<PERSON><PERSON><PERSON>", href: "/diensten/aannemer" },
  ]

  const quickLinks = [
    { name: "Tarieven", href: "/tarieven" },
    { name: "Klus plaatsen", href: "/klus-plaatsen" },
    { name: "Offerte aanvragen", href: "/offerte-aanvragen" },
    { name: "Afspraak maken", href: "/afspraak-maken" },
    { name: "Reviews", href: "/reviews" },
    { name: "Blog", href: "/blog" },
    { name: "FAQ", href: "/faq" },
  ]

  const legal = [
    { name: "Algemene voorwaarden", href: "/algemene-voorwaarden" },
    { name: "Privacybeleid", href: "/privacybeleid" },
  ]

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Phone className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">ASklussen.nl</span>
            </div>
            <p className="text-gray-300 text-sm">
              Uw betrouwbare partner voor alle klussen in en rond het huis. 
              Snel, professioneel en transparant.
            </p>
            <div className="flex space-x-4">
              <Facebook className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer transition-colors" />
              <Twitter className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer transition-colors" />
              <Instagram className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer transition-colors" />
              <Linkedin className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer transition-colors" />
            </div>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Diensten</h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.name}>
                  <Link 
                    href={service.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Snelle Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300 text-sm">085 - 123 4567</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-blue-400 mt-0.5" />
                <span className="text-gray-300 text-sm">
                  Handelskade 45<br />
                  1011 BC Amsterdam
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex space-x-6 mb-4 md:mb-0">
              {legal.map((item) => (
                <Link 
                  key={item.name}
                  href={item.href}
                  className="text-gray-400 hover:text-white text-sm transition-colors"
                >
                  {item.name}
                </Link>
              ))}
            </div>
            <p className="text-gray-400 text-sm">
              © 2024 ASklussen.nl. Alle rechten voorbehouden.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}