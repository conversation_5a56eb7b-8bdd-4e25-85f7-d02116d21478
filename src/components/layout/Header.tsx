"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, Phone, Calendar, Wrench, ChevronDown } from "lucide-react"
import NotificationBell from "@/components/ui/NotificationBell"

export default function Header() {
  const [isOpen, setIsOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const navigation = [
    { name: "Home", href: "/" },
    { 
      name: "Die<PERSON><PERSON>", 
      href: "/diensten",
      dropdown: [
        { name: "Loodgieter", href: "/diensten/loodgieter" },
        { name: "Elektricien", href: "/diensten/elektricien" },
        { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/diensten/slotenmaker" },
        { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/diensten/klusjesman" },
        { name: "Meubelmontage", href: "/diensten/meubelmontage" },
        { name: "Tegelzetter", href: "/diensten/tegelzetter" },
        { name: "Aannemer", href: "/diensten/aannemer" }
      ]
    },
    { name: "Tarieven", href: "/tarieven" },
    { name: "Reviews", href: "/reviews" },
    { name: "Over ons", href: "/over-ons" },
    { name: "Contact", href: "/contact" },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header className={`fixed top-0 w-full z-50 transition-all-300 ${
      isScrolled 
        ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50" 
        : "bg-transparent"
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 hover-lift">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <Wrench className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">✓</span>
              </div>
            </div>
            <div>
              <span className="text-xl font-bold text-gray-900">ASklussen.nl</span>
              <p className="text-xs text-gray-500 -mt-1">Professionele Klussendiensten</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigation.map((item) => (
              <div key={item.name} className="relative">
                {item.dropdown ? (
                  <div className="relative">
                    <button
                      onClick={() => setActiveDropdown(activeDropdown === item.name ? null : item.name)}
                      className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors py-2 focus-ring rounded-md px-2"
                    >
                      <span>{item.name}</span>
                      <ChevronDown className={`w-4 h-4 transition-transform ${
                        activeDropdown === item.name ? "rotate-180" : ""
                      }`} />
                    </button>
                    {activeDropdown === item.name && (
                      <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-200/50 overflow-hidden animate-slide-up">
                        {item.dropdown.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.href}
                            href={dropdownItem.href}
                            className="block px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                            onClick={() => setActiveDropdown(null)}
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className="text-gray-700 hover:text-blue-600 transition-colors py-2 focus-ring rounded-md px-2"
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Desktop CTA Buttons and Notifications */}
          <div className="hidden md:flex items-center space-x-3">
            <NotificationBell />
            <Button variant="outline" size="sm" className="btn-glow" asChild>
              <Link href="/afspraak-maken" className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>Afspraak maken</span>
              </Link>
            </Button>
            <Button size="sm" className="btn-glow shadow-lg hover:shadow-xl transition-shadow" asChild>
              <Link href="/offerte-aanvragen" className="flex items-center space-x-2">
                <span>Offerte aanvragen</span>
              </Link>
            </Button>
          </div>

          {/* Mobile menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden hover:bg-gray-100">
                <Menu className="w-5 h-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px] bg-white/95 backdrop-blur-md">
              <div className="flex flex-col space-y-4 mt-8">
                {navigation.map((item) => (
                  <div key={item.name}>
                    {item.dropdown ? (
                      <div className="space-y-2">
                        <button className="flex items-center justify-between w-full text-left font-medium text-gray-900">
                          {item.name}
                          <ChevronDown className="w-4 h-4" />
                        </button>
                        <div className="ml-4 space-y-2">
                          {item.dropdown.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.href}
                              href={dropdownItem.href}
                              className="block text-gray-600 hover:text-blue-600 transition-colors py-1"
                              onClick={() => setIsOpen(false)}
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        className="text-gray-900 hover:text-blue-600 transition-colors py-2 font-medium"
                        onClick={() => setIsOpen(false)}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}
                <div className="pt-4 border-t border-gray-200 space-y-3">
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/afspraak-maken" className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Afspraak maken</span>
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" asChild>
                    <Link href="/offerte-aanvragen" className="flex items-center space-x-2">
                      <span>Offerte aanvragen</span>
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/klus-plaatsen" className="flex items-center space-x-2">
                      <Wrench className="w-4 h-4" />
                      <span>Klus plaatsen</span>
                    </Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}